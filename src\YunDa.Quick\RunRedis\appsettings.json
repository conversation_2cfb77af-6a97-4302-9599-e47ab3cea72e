{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "RedisSetting": {"Host": "127.0.0.1", "Port": "36379", "Auth": "yunda123", "Name": "SOMS Redis Server", "ClusterType": "", "DefaultDatabaseIndex": "0", "RedisExecutablePath": "redis-server.exe", "RedisConfigPath": "redis.conf", "RedisCliPath": "redis-cli.exe", "DataDirectory": "D:\\SOMS\\Data\\Redis\\", "LogDirectory": "D:\\SOMS\\Logs\\", "BackupDirectory": "D:\\SOMS\\Backup\\Redis\\", "HealthCheckIntervalSeconds": 30, "ProcessCheckIntervalSeconds": 3, "MaxRestartAttempts": 3, "RestartDelaySeconds": 10, "ConnectionTimeoutSeconds": 30, "MaxConnectionPoolSize": 100, "MinConnectionPoolSize": 5, "MaxMemoryUsageMB": 2048, "MaxActiveConnections": 80, "MaxCpuUsagePercent": 85.0, "EnableAutoBackup": true, "BackupRetentionDays": 7, "EnablePerformanceOptimization": true, "OptimizationIntervalHours": 24, "EnableEmailAlerts": false, "AlertEmailRecipients": "", "SmtpServer": "", "SmtpPort": 587, "SmtpUsername": "", "SmtpPassword": "", "MaxMemoryPolicy": "allkeys-lru", "EnablePersistence": true, "SaveConfiguration": "900 1 300 10 60 10000", "DatabaseCount": 16, "ClientTimeoutSeconds": 300, "EnableSlowLog": true, "SlowLogMaxLength": 128, "SlowLogSlowerThanMicroseconds": 10000}}