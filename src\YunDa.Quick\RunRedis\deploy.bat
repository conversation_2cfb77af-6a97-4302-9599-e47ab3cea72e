@echo off
echo ========================================
echo Redis Management Service Deployment
echo ========================================

set PROJECT_NAME=RunRedis
set SERVICE_NAME=SOMS Redis Service
set INSTALL_DIR=D:\SOMS\Services\Redis

echo.
echo 1. Building project...
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo 2. Publishing project...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "%INSTALL_DIR%"
if %ERRORLEVEL% neq 0 (
    echo Publish failed!
    pause
    exit /b 1
)

echo.
echo 3. Copying configuration files...
copy "appsettings.json" "%INSTALL_DIR%\"
copy "appsettings.Development.json" "%INSTALL_DIR%\" 2>nul

echo.
echo 4. Creating directories...
mkdir "D:\SOMS\Data\Redis" 2>nul
mkdir "D:\SOMS\Logs" 2>nul
mkdir "D:\SOMS\Backup\Redis" 2>nul

echo.
echo 5. Checking if service exists...
sc query "%SERVICE_NAME%" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo Service exists, stopping and deleting...
    sc stop "%SERVICE_NAME%"
    timeout /t 5 /nobreak >nul
    sc delete "%SERVICE_NAME%"
    timeout /t 2 /nobreak >nul
)

echo.
echo 6. Installing Windows service...
sc create "%SERVICE_NAME%" binPath="%INSTALL_DIR%\SOMS内存数据库.exe" start=auto DisplayName="SOMS Redis Management Service"
if %ERRORLEVEL% neq 0 (
    echo Service installation failed!
    pause
    exit /b 1
)

echo.
echo 7. Starting service...
sc start "%SERVICE_NAME%"
if %ERRORLEVEL% neq 0 (
    echo Service start failed!
    echo You can start it manually from Services.msc
)

echo.
echo ========================================
echo Deployment completed successfully!
echo ========================================
echo.
echo Service Name: %SERVICE_NAME%
echo Install Directory: %INSTALL_DIR%
echo.
echo You can:
echo - Check service status: sc query "%SERVICE_NAME%"
echo - Stop service: sc stop "%SERVICE_NAME%"
echo - Start service: sc start "%SERVICE_NAME%"
echo - View logs: Check D:\SOMS\Logs\
echo.
pause
