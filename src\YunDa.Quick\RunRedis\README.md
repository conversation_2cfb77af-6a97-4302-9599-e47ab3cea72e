# Enhanced Redis Management Service

## 概述

增强的Redis管理服务是一个全面的Redis实例管理解决方案，具有高级功能，包括崩溃恢复、性能优化、健康监控和自动维护。

## 主要功能

### 1. 初始设置和数据库初始化
- **自动数据库创建**，具有适当的配置验证
- **目录管理**，自动创建数据和日志目录
- **初始配置优化**，根据最佳实践设置Redis参数
- **灵活的路径配置**，支持配置文件和命令行参数

### 2. 崩溃恢复和进程管理
- **自动崩溃检测**，通过进程和连接监控
- **智能重启程序**，具有适当的清理机制
- **数据完整性检查**，重启前后的验证
- **优雅关闭**，可配置超时时间
- **锁文件和诊断数据清理**
- **最大重启尝试限制**，防止无限循环

### 3. 性能优化
- **内存使用监控**和优化
- **连接池管理**，可配置限制
- **查询性能监控**和优化
- **自动数据库压缩**和重新索引
- **资源分配调优**，基于系统能力
- **定时优化**，可配置间隔

### 4. 健康监控
- **全面的健康检查**，包括进程、连接、内存、CPU
- **性能指标收集**，命中率、连接数、响应时间
- **慢日志监控**，识别性能瓶颈
- **阈值监控**，自动检测异常情况
- **实时状态报告**

### 5. 自动备份和维护
- **自动备份**，可配置间隔和保留策略
- **备份验证**，确保备份完整性
- **日志文件清理**，防止磁盘空间耗尽
- **过期数据清理**
- **内存碎片整理**

### 6. Windows服务支持
- **Windows服务集成**，支持系统启动自动运行
- **服务生命周期管理**
- **优雅的启动和关闭**
- **服务状态监控**

## 配置选项

### 基本连接设置
```json
{
  "Host": "127.0.0.1",
  "Port": "36379",
  "Auth": "yunda123",
  "DefaultDatabaseIndex": "0"
}
```

### 文件路径配置
```json
{
  "RedisExecutablePath": "redis-server.exe",
  "RedisConfigPath": "redis.conf",
  "RedisCliPath": "redis-cli.exe",
  "DataDirectory": "D:\\SOMS\\Data\\Redis\\",
  "LogDirectory": "D:\\SOMS\\Logs\\",
  "BackupDirectory": "D:\\SOMS\\Backup\\Redis\\"
}
```

### 监控设置
```json
{
  "HealthCheckIntervalSeconds": 30,
  "ProcessCheckIntervalSeconds": 3,
  "MaxRestartAttempts": 3,
  "RestartDelaySeconds": 10,
  "ConnectionTimeoutSeconds": 30
}
```

### 性能设置
```json
{
  "MaxConnectionPoolSize": 100,
  "MinConnectionPoolSize": 5,
  "MaxMemoryUsageMB": 2048,
  "MaxActiveConnections": 80,
  "MaxCpuUsagePercent": 85.0
}
```

### 维护设置
```json
{
  "EnableAutoBackup": true,
  "BackupRetentionDays": 7,
  "EnablePerformanceOptimization": true,
  "OptimizationIntervalHours": 24
}
```

### Redis高级设置
```json
{
  "MaxMemoryPolicy": "allkeys-lru",
  "EnablePersistence": true,
  "SaveConfiguration": "900 1 300 10 60 10000",
  "DatabaseCount": 16,
  "ClientTimeoutSeconds": 300,
  "EnableSlowLog": true,
  "SlowLogMaxLength": 128,
  "SlowLogSlowerThanMicroseconds": 10000
}
```

## 使用方法

### 运行增强服务

1. **作为控制台应用程序运行**：
   ```bash
   dotnet run --project RunRedis
   ```

2. **作为Windows服务安装**：
   ```bash
   sc create "SOMS Redis Service" binPath="C:\Path\To\RunRedis.exe"
   sc start "SOMS Redis Service"
   ```

3. **使用发布版本**：
   ```bash
   dotnet publish -c Release -r win-x64 --self-contained
   ```

### 配置Redis

1. **编辑appsettings.json**，根据您的环境调整设置
2. **确保Redis可执行文件**在指定路径或PATH环境变量中
3. **创建必要的目录**，或让服务自动创建
4. **配置防火墙**，允许Redis端口访问

## 架构

### 服务组件

1. **RedisProcessManager**: 处理Redis进程生命周期
2. **RedisHealthMonitor**: 执行全面的健康检查
3. **RedisPerformanceOptimizer**: 处理性能优化
4. **EnhancedWorker**: 协调所有服务和监控
5. **RedisWorkerService**: .NET Core后台服务包装器

### 监控流程

```
启动 → 初始设置 → 数据库初始化 → 监控循环
                                    ↓
健康检查 ← 性能优化 ← 进程检查 ← 备份管理
    ↓
问题检测 → 纠正措施 → 警报/日志
```

## 日志记录

服务提供结构化日志记录，具有多个级别：

- **Information**: 正常操作事件
- **Warning**: 潜在问题和纠正措施
- **Error**: 错误情况和恢复尝试
- **Fatal**: 需要立即关注的严重错误

日志文件位置：`{LogDirectory}/RunRedis-{Date}.txt`

## 故障排除

### 常见问题

1. **Redis无法启动**
   - 检查Redis可执行文件路径
   - 验证端口是否被占用
   - 检查数据目录权限

2. **高内存使用**
   - 检查内存策略配置
   - 验证数据过期设置
   - 考虑增加内存限制

3. **连接问题**
   - 验证主机和端口设置
   - 检查防火墙配置
   - 确认认证设置

### 手动干预

如果服务达到最大重启尝试次数：

1. 检查Redis日志以获取错误详细信息
2. 验证系统资源和磁盘空间
3. 检查数据目录是否损坏
4. 考虑手动运行Redis修复
5. 解决问题后重启管理服务

## 性能建议

1. **内存**: 为Redis分配至少2GB RAM
2. **存储**: 使用SSD存储以获得更好的I/O性能
3. **网络**: 确保应用程序和Redis之间的低延迟
4. **索引**: 定期审查和优化数据结构
5. **监控**: 保持合理的健康检查间隔（30-60秒）

## 安全考虑

1. **认证**: 始终设置强密码
2. **网络**: 限制Redis访问到受信任的网络
3. **备份**: 加密敏感数据的备份
4. **日志**: 保护日志文件免受未授权访问
5. **更新**: 定期更新Redis到最新版本

## 更新历史

### v2.0 (当前版本)
- 完全重构为模块化架构
- 添加全面的健康监控
- 实现自动性能优化
- 添加Windows服务支持
- 增强错误处理和恢复
- 改进配置管理
- 添加自动备份功能

### v1.0 (原始版本)
- 基本Redis启动和监控
- 简单的资源检查
- 基础日志记录
