﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RunMongoDB
{
    public class MongoDBSettings
    {
        public string Host { get; set; } = "127.0.0.1";
        public string Port { get; set; } = "37017";
        public string DatabaseName { get; set; } = "isas_mongodb";
        public string IsAuth { get; set; } = "false";
        public string UserName { get; set; } = "isasAdmin";
        public string PassWord { get; set; } = "123456";

        // Enhanced configuration properties
        public string MongoDBExecutablePath { get; set; } = @"D:\SOMS\大数据库\mongod.exe";
        public string MongoDBConfigPath { get; set; } = @"D:\SOMS\大数据库\mongod.cfg";
        public string DataDirectory { get; set; } = @"D:\SOMS\Data\MongoDB\data\";
        public string LogDirectory { get; set; } = @"D:\SOMS\Logs\";
        public string BackupDirectory { get; set; } = @"D:\SOMS\Backup\MongoDB\";

        // Monitoring and performance settings
        public int HealthCheckIntervalSeconds { get; set; } = 30;
        public int ProcessCheckIntervalSeconds { get; set; } = 3;
        public int MaxRestartAttempts { get; set; } = 3;
        public int RestartDelaySeconds { get; set; } = 10;
        public int ConnectionTimeoutSeconds { get; set; } = 30;
        public int MaxConnectionPoolSize { get; set; } = 100;
        public int MinConnectionPoolSize { get; set; } = 5;

        // Performance thresholds
        public long MaxMemoryUsageMB { get; set; } = 4096; // 4GB
        public int MaxActiveConnections { get; set; } = 80;
        public double MaxCpuUsagePercent { get; set; } = 85.0;

        // Backup and maintenance settings
        public bool EnableAutoBackup { get; set; } = true;
        public int BackupRetentionDays { get; set; } = 7;
        public bool EnablePerformanceOptimization { get; set; } = true;
        public int OptimizationIntervalHours { get; set; } = 24;

        // Alert settings
        public bool EnableEmailAlerts { get; set; } = false;
        public string AlertEmailRecipients { get; set; } = "";
        public string SmtpServer { get; set; } = "";
        public int SmtpPort { get; set; } = 587;
        public string SmtpUsername { get; set; } = "";
        public string SmtpPassword { get; set; } = "";
    }
}
