using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBConfigurationService
    {
        private readonly MongoDBSettings _settings;

        public MongoDBConfigurationService(MongoDBSettings settings)
        {
            _settings = settings;
        }

        /// <summary>
        /// Resolves and validates MongoDB configuration paths
        /// </summary>
        public MongoDBPathConfiguration ResolveConfigurationPaths()
        {
            var config = new MongoDBPathConfiguration();

            try
            {
                // Get the application base directory
                var baseDirectory = GetApplicationBaseDirectory();
                Log.Information("Application base directory: {BaseDirectory}", baseDirectory);

                // Resolve MongoDB executable path
                config.MongoDBExecutablePath = ResolveMongoDBExecutablePath(baseDirectory);
                
                // Resolve MongoDB configuration file path
                config.MongoDBConfigPath = ResolveMongoDBConfigPath(baseDirectory);
                
                // Resolve data directory
                config.DataDirectory = ResolveDataDirectory();
                
                // Resolve log directory
                config.LogDirectory = ResolveLogDirectory();
                
                // Resolve backup directory
                config.BackupDirectory = ResolveBackupDirectory();

                // Validate all paths
                ValidateConfiguration(config);

                Log.Information("MongoDB configuration paths resolved successfully");
                return config;
            }
            catch (Exception ex)
            {
                Log.Error("Error resolving MongoDB configuration paths: {Error}", ex.Message);
                throw;
            }
        }

        private string GetApplicationBaseDirectory()
        {
            // Try multiple methods to get the base directory
            var candidates = new[]
            {
                AppDomain.CurrentDomain.BaseDirectory,
                Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
                Directory.GetCurrentDirectory(),
                Environment.CurrentDirectory
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                if (Directory.Exists(candidate))
                {
                    return candidate;
                }
            }

            throw new DirectoryNotFoundException("Could not determine application base directory");
        }

        private string ResolveMongoDBExecutablePath(string baseDirectory)
        {
            // Priority order for finding MongoDB executable
            var searchPaths = new[]
            {
                _settings.MongoDBExecutablePath, // From configuration
                Path.Combine(baseDirectory, "MongoDB", "bin", "mongod.exe"),
                Path.Combine(baseDirectory, "大数据库", "mongod.exe"),
                @"D:\SOMS\大数据库\mongod.exe",
                @"C:\Program Files\MongoDB\Server\4.4\bin\mongod.exe",
                @"C:\Program Files\MongoDB\Server\5.0\bin\mongod.exe",
                @"C:\Program Files\MongoDB\Server\6.0\bin\mongod.exe",
                @"C:\MongoDB\bin\mongod.exe"
            };

            foreach (var path in searchPaths.Where(p => !string.IsNullOrEmpty(p)))
            {
                if (File.Exists(path))
                {
                    Log.Information("Found MongoDB executable at: {Path}", path);
                    return path;
                }
            }

            // If not found, use the configured path anyway (might be installed later)
            Log.Warning("MongoDB executable not found. Using configured path: {Path}", _settings.MongoDBExecutablePath);
            return _settings.MongoDBExecutablePath;
        }

        private string ResolveMongoDBConfigPath(string baseDirectory)
        {
            // Priority order for finding MongoDB config file
            var searchPaths = new[]
            {
                _settings.MongoDBConfigPath, // From configuration
                Path.Combine(baseDirectory, "mongod.cfg"),
                Path.Combine(baseDirectory, "sample-mongod.cfg"),
                Path.Combine(baseDirectory, "MongoDB", "mongod.cfg"),
                Path.Combine(baseDirectory, "大数据库", "mongod.cfg"),
                @"D:\SOMS\大数据库\mongod.cfg"
            };

            foreach (var path in searchPaths.Where(p => !string.IsNullOrEmpty(p)))
            {
                if (File.Exists(path))
                {
                    Log.Information("Found MongoDB config file at: {Path}", path);
                    return path;
                }
            }

            // If no config file found, return null to use command line arguments
            Log.Information("No MongoDB config file found. Will use command line arguments.");
            return null;
        }

        private string ResolveDataDirectory()
        {
            var candidates = new[]
            {
                _settings.DataDirectory,
                @"D:\SOMS\Data\MongoDB\data\",
                Path.Combine(GetApplicationBaseDirectory(), "Data", "MongoDB", "data"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MongoDB", "data")
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                try
                {
                    if (!Directory.Exists(candidate))
                    {
                        Directory.CreateDirectory(candidate);
                    }
                    Log.Information("Using data directory: {Directory}", candidate);
                    return candidate;
                }
                catch (Exception ex)
                {
                    Log.Warning("Could not create data directory {Directory}: {Error}", candidate, ex.Message);
                }
            }

            throw new DirectoryNotFoundException("Could not resolve or create MongoDB data directory");
        }

        private string ResolveLogDirectory()
        {
            var candidates = new[]
            {
                _settings.LogDirectory,
                @"D:\SOMS\Logs\",
                Path.Combine(GetApplicationBaseDirectory(), "Logs"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MongoDB", "logs")
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                try
                {
                    if (!Directory.Exists(candidate))
                    {
                        Directory.CreateDirectory(candidate);
                    }
                    Log.Information("Using log directory: {Directory}", candidate);
                    return candidate;
                }
                catch (Exception ex)
                {
                    Log.Warning("Could not create log directory {Directory}: {Error}", candidate, ex.Message);
                }
            }

            throw new DirectoryNotFoundException("Could not resolve or create MongoDB log directory");
        }

        private string ResolveBackupDirectory()
        {
            var candidates = new[]
            {
                _settings.BackupDirectory,
                @"D:\SOMS\Backup\MongoDB\",
                Path.Combine(GetApplicationBaseDirectory(), "Backup", "MongoDB"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "MongoDB", "backup")
            };

            foreach (var candidate in candidates.Where(c => !string.IsNullOrEmpty(c)))
            {
                try
                {
                    if (!Directory.Exists(candidate))
                    {
                        Directory.CreateDirectory(candidate);
                    }
                    Log.Information("Using backup directory: {Directory}", candidate);
                    return candidate;
                }
                catch (Exception ex)
                {
                    Log.Warning("Could not create backup directory {Directory}: {Error}", candidate, ex.Message);
                }
            }

            throw new DirectoryNotFoundException("Could not resolve or create MongoDB backup directory");
        }

        private void ValidateConfiguration(MongoDBPathConfiguration config)
        {
            var issues = new List<string>();

            // Check MongoDB executable
            if (string.IsNullOrEmpty(config.MongoDBExecutablePath) || !File.Exists(config.MongoDBExecutablePath))
            {
                issues.Add($"MongoDB executable not found: {config.MongoDBExecutablePath}");
            }

            // Check directories
            if (string.IsNullOrEmpty(config.DataDirectory) || !Directory.Exists(config.DataDirectory))
            {
                issues.Add($"Data directory not accessible: {config.DataDirectory}");
            }

            if (string.IsNullOrEmpty(config.LogDirectory) || !Directory.Exists(config.LogDirectory))
            {
                issues.Add($"Log directory not accessible: {config.LogDirectory}");
            }

            if (string.IsNullOrEmpty(config.BackupDirectory) || !Directory.Exists(config.BackupDirectory))
            {
                issues.Add($"Backup directory not accessible: {config.BackupDirectory}");
            }

            if (issues.Count > 0)
            {
                Log.Warning("Configuration validation issues found: {Issues}", string.Join(", ", issues));
                // Don't throw exception, just log warnings - some issues might be resolved at runtime
            }
        }

        /// <summary>
        /// Parses MongoDB configuration file if available
        /// </summary>
        public MongoDBConfigFileSettings ParseConfigFile(string configPath)
        {
            var settings = new MongoDBConfigFileSettings();

            if (string.IsNullOrEmpty(configPath) || !File.Exists(configPath))
            {
                Log.Information("No MongoDB config file to parse");
                return settings;
            }

            try
            {
                Log.Information("Parsing MongoDB config file: {ConfigPath}", configPath);
                var lines = File.ReadAllLines(configPath);
                
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith("#"))
                        continue;

                    // Simple YAML-like parsing for basic settings
                    if (trimmedLine.Contains("port:"))
                    {
                        var portValue = ExtractValue(trimmedLine);
                        if (int.TryParse(portValue, out int port))
                        {
                            settings.Port = port;
                        }
                    }
                    else if (trimmedLine.Contains("bindIp:"))
                    {
                        settings.BindIp = ExtractValue(trimmedLine).Trim('"');
                    }
                    else if (trimmedLine.Contains("dbPath:"))
                    {
                        settings.DbPath = ExtractValue(trimmedLine).Trim('"');
                    }
                    else if (trimmedLine.Contains("path:") && trimmedLine.Contains("systemLog"))
                    {
                        settings.LogPath = ExtractValue(trimmedLine).Trim('"');
                    }
                }

                Log.Information("MongoDB config file parsed successfully");
                return settings;
            }
            catch (Exception ex)
            {
                Log.Error("Error parsing MongoDB config file: {Error}", ex.Message);
                return settings;
            }
        }

        private string ExtractValue(string line)
        {
            var colonIndex = line.IndexOf(':');
            if (colonIndex >= 0 && colonIndex < line.Length - 1)
            {
                return line.Substring(colonIndex + 1).Trim();
            }
            return string.Empty;
        }
    }

    public class MongoDBPathConfiguration
    {
        public string MongoDBExecutablePath { get; set; }
        public string MongoDBConfigPath { get; set; }
        public string DataDirectory { get; set; }
        public string LogDirectory { get; set; }
        public string BackupDirectory { get; set; }
    }

    public class MongoDBConfigFileSettings
    {
        public int? Port { get; set; }
        public string BindIp { get; set; }
        public string DbPath { get; set; }
        public string LogPath { get; set; }
    }
}
