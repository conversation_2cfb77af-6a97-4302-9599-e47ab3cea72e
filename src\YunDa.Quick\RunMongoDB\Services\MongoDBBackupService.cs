using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBBackupService
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoClient _mongoClient;
        private readonly MongoDBConfigurationService _configService;
        private readonly MongoDBPathConfiguration _pathConfig;

        public MongoDBBackupService(MongoDBSettings settings)
        {
            _settings = settings;
            _configService = new MongoDBConfigurationService(settings);
            _pathConfig = _configService.ResolveConfigurationPaths();
            
            var clientSettings = new MongoClientSettings
            {
                Server = new MongoServerAddress(_settings.Host, int.Parse(_settings.Port)),
                ConnectTimeout = TimeSpan.FromSeconds(_settings.ConnectionTimeoutSeconds)
            };

            if (_settings.IsAuth == "true")
            {
                clientSettings.Credential = MongoCredential.CreateCredential(
                    "admin", _settings.UserName, _settings.PassWord);
            }

            _mongoClient = new MongoClient(clientSettings);
        }

        public async Task<BackupResult> CreateBackupAsync(string backupName = null)
        {
            var result = new BackupResult
            {
                StartTime = DateTime.Now,
                BackupName = backupName ?? $"backup_{DateTime.Now:yyyyMMdd_HHmmss}"
            };

            try
            {
                if (!_settings.EnableAutoBackup)
                {
                    result.Success = false;
                    result.ErrorMessage = "Auto backup is disabled";
                    return result;
                }

                Log.Information("Starting MongoDB backup: {BackupName}", result.BackupName);

                // Ensure backup directory exists
                if (!Directory.Exists(_pathConfig.BackupDirectory))
                {
                    Directory.CreateDirectory(_pathConfig.BackupDirectory);
                }

                // Create backup subdirectory
                var backupPath = Path.Combine(_pathConfig.BackupDirectory, result.BackupName);
                Directory.CreateDirectory(backupPath);

                // Perform database backup using mongodump
                var success = await PerformMongoDumpAsync(backupPath);
                
                if (success)
                {
                    // Verify backup integrity
                    var isValid = await VerifyBackupIntegrityAsync(backupPath);
                    
                    if (isValid)
                    {
                        result.Success = true;
                        result.BackupPath = backupPath;
                        result.BackupSizeBytes = GetDirectorySize(backupPath);
                        
                        Log.Information("Backup completed successfully: {BackupPath} ({SizeMB} MB)", 
                            backupPath, result.BackupSizeBytes / (1024 * 1024));
                        
                        // Clean up old backups
                        await CleanupOldBackupsAsync();
                    }
                    else
                    {
                        result.Success = false;
                        result.ErrorMessage = "Backup integrity verification failed";
                        
                        // Clean up failed backup
                        try
                        {
                            Directory.Delete(backupPath, true);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not clean up failed backup directory: {Error}", ex.Message);
                        }
                    }
                }
                else
                {
                    result.Success = false;
                    result.ErrorMessage = "mongodump operation failed";
                }

                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Log.Error("Error during backup creation: {Error}", ex.Message);
                return result;
            }
        }

        private async Task<bool> PerformMongoDumpAsync(string backupPath)
        {
            try
            {
                var mongoDumpPath = Path.Combine(Path.GetDirectoryName(_pathConfig.MongoDBExecutablePath), "mongodump.exe");
                
                if (!File.Exists(mongoDumpPath))
                {
                    Log.Error("mongodump.exe not found at: {MongoDumpPath}", mongoDumpPath);
                    return false;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = mongoDumpPath,
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                // Add connection parameters
                startInfo.ArgumentList.Add("--host");
                startInfo.ArgumentList.Add($"{_settings.Host}:{_settings.Port}");
                
                startInfo.ArgumentList.Add("--db");
                startInfo.ArgumentList.Add(_settings.DatabaseName);
                
                startInfo.ArgumentList.Add("--out");
                startInfo.ArgumentList.Add(backupPath);

                // Add authentication if enabled
                if (_settings.IsAuth == "true")
                {
                    startInfo.ArgumentList.Add("--username");
                    startInfo.ArgumentList.Add(_settings.UserName);
                    startInfo.ArgumentList.Add("--password");
                    startInfo.ArgumentList.Add(_settings.PassWord);
                    startInfo.ArgumentList.Add("--authenticationDatabase");
                    startInfo.ArgumentList.Add("admin");
                }

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        var error = await process.StandardError.ReadToEndAsync();
                        
                        await process.WaitForExitAsync();
                        
                        if (process.ExitCode == 0)
                        {
                            Log.Debug("mongodump completed successfully");
                            return true;
                        }
                        else
                        {
                            Log.Error("mongodump failed with exit code {ExitCode}. Error: {Error}", 
                                process.ExitCode, error);
                            return false;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error("Error executing mongodump: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> VerifyBackupIntegrityAsync(string backupPath)
        {
            try
            {
                // Check if backup directory contains expected files
                var dbBackupPath = Path.Combine(backupPath, _settings.DatabaseName);
                
                if (!Directory.Exists(dbBackupPath))
                {
                    Log.Error("Database backup directory not found: {DbBackupPath}", dbBackupPath);
                    return false;
                }

                var bsonFiles = Directory.GetFiles(dbBackupPath, "*.bson");
                var metadataFiles = Directory.GetFiles(dbBackupPath, "*.metadata.json");

                if (bsonFiles.Length == 0)
                {
                    Log.Warning("No BSON files found in backup - database might be empty");
                }

                // Verify each BSON file can be read
                foreach (var bsonFile in bsonFiles)
                {
                    try
                    {
                        using (var fileStream = File.OpenRead(bsonFile))
                        {
                            // Try to read the first few bytes to verify file integrity
                            var buffer = new byte[1024];
                            await fileStream.ReadAsync(buffer, 0, buffer.Length);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error("Backup file integrity check failed for {BsonFile}: {Error}", bsonFile, ex.Message);
                        return false;
                    }
                }

                Log.Debug("Backup integrity verification passed for {BackupPath}", backupPath);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error during backup integrity verification: {Error}", ex.Message);
                return false;
            }
        }

        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                if (!Directory.Exists(_pathConfig.BackupDirectory))
                {
                    return;
                }

                var backupDirectories = Directory.GetDirectories(_pathConfig.BackupDirectory)
                    .Select(d => new DirectoryInfo(d))
                    .Where(d => d.Name.StartsWith("backup_"))
                    .OrderByDescending(d => d.CreationTime)
                    .ToList();

                var cutoffDate = DateTime.Now.AddDays(-_settings.BackupRetentionDays);
                var directoriesToDelete = backupDirectories
                    .Where(d => d.CreationTime < cutoffDate)
                    .ToList();

                foreach (var directory in directoriesToDelete)
                {
                    try
                    {
                        directory.Delete(true);
                        Log.Information("Deleted old backup: {BackupDirectory}", directory.Name);
                    }
                    catch (Exception ex)
                    {
                        Log.Warning("Could not delete old backup {BackupDirectory}: {Error}", 
                            directory.Name, ex.Message);
                    }
                }

                if (directoriesToDelete.Count > 0)
                {
                    Log.Information("Cleaned up {Count} old backup(s)", directoriesToDelete.Count);
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during backup cleanup: {Error}", ex.Message);
            }
        }

        private long GetDirectorySize(string directoryPath)
        {
            try
            {
                var directory = new DirectoryInfo(directoryPath);
                return directory.GetFiles("*", SearchOption.AllDirectories).Sum(file => file.Length);
            }
            catch
            {
                return 0;
            }
        }

        public async Task<RestoreResult> RestoreBackupAsync(string backupName)
        {
            var result = new RestoreResult
            {
                StartTime = DateTime.Now,
                BackupName = backupName
            };

            try
            {
                Log.Information("Starting MongoDB restore from backup: {BackupName}", backupName);

                var backupPath = Path.Combine(_pathConfig.BackupDirectory, backupName);
                
                if (!Directory.Exists(backupPath))
                {
                    result.Success = false;
                    result.ErrorMessage = $"Backup directory not found: {backupPath}";
                    return result;
                }

                // Verify backup before restore
                var isValid = await VerifyBackupIntegrityAsync(backupPath);
                if (!isValid)
                {
                    result.Success = false;
                    result.ErrorMessage = "Backup integrity verification failed";
                    return result;
                }

                // Perform restore using mongorestore
                var success = await PerformMongoRestoreAsync(backupPath);
                
                result.Success = success;
                if (!success)
                {
                    result.ErrorMessage = "mongorestore operation failed";
                }

                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;

                if (success)
                {
                    Log.Information("Restore completed successfully from backup: {BackupName}", backupName);
                }

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                
                Log.Error("Error during backup restore: {Error}", ex.Message);
                return result;
            }
        }

        private async Task<bool> PerformMongoRestoreAsync(string backupPath)
        {
            try
            {
                var mongoRestorePath = Path.Combine(Path.GetDirectoryName(_pathConfig.MongoDBExecutablePath), "mongorestore.exe");
                
                if (!File.Exists(mongoRestorePath))
                {
                    Log.Error("mongorestore.exe not found at: {MongoRestorePath}", mongoRestorePath);
                    return false;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = mongoRestorePath,
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                // Add connection parameters
                startInfo.ArgumentList.Add("--host");
                startInfo.ArgumentList.Add($"{_settings.Host}:{_settings.Port}");
                
                startInfo.ArgumentList.Add("--db");
                startInfo.ArgumentList.Add(_settings.DatabaseName);
                
                startInfo.ArgumentList.Add("--drop"); // Drop existing collections before restore
                
                startInfo.ArgumentList.Add(Path.Combine(backupPath, _settings.DatabaseName));

                // Add authentication if enabled
                if (_settings.IsAuth == "true")
                {
                    startInfo.ArgumentList.Add("--username");
                    startInfo.ArgumentList.Add(_settings.UserName);
                    startInfo.ArgumentList.Add("--password");
                    startInfo.ArgumentList.Add(_settings.PassWord);
                    startInfo.ArgumentList.Add("--authenticationDatabase");
                    startInfo.ArgumentList.Add("admin");
                }

                using (var process = Process.Start(startInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        var error = await process.StandardError.ReadToEndAsync();
                        
                        await process.WaitForExitAsync();
                        
                        if (process.ExitCode == 0)
                        {
                            Log.Debug("mongorestore completed successfully");
                            return true;
                        }
                        else
                        {
                            Log.Error("mongorestore failed with exit code {ExitCode}. Error: {Error}", 
                                process.ExitCode, error);
                            return false;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error("Error executing mongorestore: {Error}", ex.Message);
                return false;
            }
        }
    }

    public class BackupResult
    {
        public string BackupName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public string BackupPath { get; set; }
        public long BackupSizeBytes { get; set; }
    }

    public class RestoreResult
    {
        public string BackupName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
    }
}
