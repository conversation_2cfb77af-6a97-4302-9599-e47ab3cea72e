using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;
using RunRedis.Models;

namespace RunRedis.Services
{
    public class RedisPerformanceOptimizer
    {
        private readonly RedisSetting _settings;
        private readonly RedisProcessManager _processManager;
        private readonly RedisHealthMonitor _healthMonitor;
        private DateTime _lastOptimization = DateTime.MinValue;

        public RedisPerformanceOptimizer(RedisSetting settings, RedisProcessManager processManager, RedisHealthMonitor healthMonitor)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _processManager = processManager ?? throw new ArgumentNullException(nameof(processManager));
            _healthMonitor = healthMonitor ?? throw new ArgumentNullException(nameof(healthMonitor));
        }

        public async Task<bool> OptimizeRedisAsync(bool force = false)
        {
            try
            {
                // Check if optimization is needed
                if (!force && !ShouldOptimize())
                {
                    return true;
                }

                Log.Information("Starting Redis performance optimization");

                var healthCheck = await _healthMonitor.PerformHealthCheckAsync();
                if (!healthCheck.IsHealthy)
                {
                    Log.Warning("Skipping optimization - Redis is not healthy");
                    return false;
                }

                bool success = true;

                // Memory optimization
                success &= await OptimizeMemoryAsync(healthCheck);

                // Connection optimization
                success &= await OptimizeConnectionsAsync(healthCheck);

                // Persistence optimization
                success &= await OptimizePersistenceAsync(healthCheck);

                // Slow log optimization
                success &= await OptimizeSlowLogAsync(healthCheck);

                // General configuration optimization
                success &= await OptimizeGeneralConfigAsync();

                // Cleanup operations
                success &= await PerformCleanupAsync();

                if (success)
                {
                    _lastOptimization = DateTime.Now;
                    Log.Information("Redis optimization completed successfully");
                }
                else
                {
                    Log.Warning("Redis optimization completed with some issues");
                }

                return success;
            }
            catch (Exception ex)
            {
                Log.Error("Error during Redis optimization: {Error}", ex.Message);
                return false;
            }
        }

        private bool ShouldOptimize()
        {
            if (_lastOptimization == DateTime.MinValue)
                return true;

            var timeSinceLastOptimization = DateTime.Now - _lastOptimization;
            return timeSinceLastOptimization.TotalHours >= _settings.OptimizationIntervalHours;
        }

        private async Task<bool> OptimizeMemoryAsync(HealthCheckResult healthCheck)
        {
            try
            {
                Log.Information("Optimizing Redis memory settings");

                // Set memory policy if not already configured
                var currentPolicy = await _processManager.ExecuteRedisCliCommandAsync("CONFIG GET maxmemory-policy");
                if (string.IsNullOrEmpty(currentPolicy) || !currentPolicy.Contains(_settings.MaxMemoryPolicy))
                {
                    await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET maxmemory-policy {_settings.MaxMemoryPolicy}");
                    Log.Information("Set memory policy to: {Policy}", _settings.MaxMemoryPolicy);
                }

                // Set max memory if configured
                if (_settings.MaxMemoryUsageMB > 0)
                {
                    var maxMemoryBytes = _settings.MaxMemoryUsageMB * 1024 * 1024;
                    await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET maxmemory {maxMemoryBytes}");
                    Log.Information("Set max memory to: {MaxMemoryMB}MB", _settings.MaxMemoryUsageMB);
                }

                // If memory usage is high, trigger cleanup
                if (healthCheck.MemoryUsageMB > _settings.MaxMemoryUsageMB * 0.8)
                {
                    Log.Information("High memory usage detected, triggering cleanup");
                    
                    // Expire keys that should be expired
                    await _processManager.ExecuteRedisCliCommandAsync("EVAL \"return redis.call('eval', 'for i=1,1000 do if redis.call(\\\"randomkey\\\") then redis.call(\\\"expire\\\", redis.call(\\\"randomkey\\\"), 1) end end return 1', 0)\" 0");
                    
                    // Force garbage collection if supported
                    await _processManager.ExecuteRedisCliCommandAsync("MEMORY PURGE");
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing memory: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> OptimizeConnectionsAsync(HealthCheckResult healthCheck)
        {
            try
            {
                Log.Information("Optimizing Redis connection settings");

                // Set client timeout
                await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET timeout {_settings.ClientTimeoutSeconds}");

                // Set TCP keepalive
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET tcp-keepalive 300");

                // If too many connections, consider adjusting timeout
                if (healthCheck.ActiveConnections > _settings.MaxActiveConnections * 0.8)
                {
                    Log.Warning("High connection count detected: {Count}", healthCheck.ActiveConnections);
                    
                    // Reduce timeout for idle connections
                    var reducedTimeout = Math.Max(60, _settings.ClientTimeoutSeconds / 2);
                    await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET timeout {reducedTimeout}");
                    Log.Information("Reduced client timeout to {Timeout} seconds due to high connection count", reducedTimeout);
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing connections: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> OptimizePersistenceAsync(HealthCheckResult healthCheck)
        {
            try
            {
                Log.Information("Optimizing Redis persistence settings");

                if (_settings.EnablePersistence)
                {
                    // Configure save intervals
                    await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET save \"{_settings.SaveConfiguration}\"");
                    
                    // Enable RDB compression
                    await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET rdbcompression yes");
                    
                    // Enable RDB checksum
                    await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET rdbchecksum yes");
                    
                    Log.Information("Configured persistence with save intervals: {SaveConfig}", _settings.SaveConfiguration);
                }
                else
                {
                    // Disable persistence for better performance
                    await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET save \"\"");
                    Log.Information("Disabled persistence for better performance");
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing persistence: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> OptimizeSlowLogAsync(HealthCheckResult healthCheck)
        {
            try
            {
                Log.Information("Optimizing Redis slow log settings");

                if (_settings.EnableSlowLog)
                {
                    // Configure slow log
                    await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET slowlog-max-len {_settings.SlowLogMaxLength}");
                    await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET slowlog-log-slower-than {_settings.SlowLogSlowerThanMicroseconds}");
                    
                    // If slow log is getting full, clear old entries
                    if (healthCheck.SlowLogLength > _settings.SlowLogMaxLength * 0.9)
                    {
                        await _processManager.ExecuteRedisCliCommandAsync("SLOWLOG RESET");
                        Log.Information("Cleared slow log due to high entry count");
                    }
                }
                else
                {
                    // Disable slow log
                    await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET slowlog-max-len 0");
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing slow log: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> OptimizeGeneralConfigAsync()
        {
            try
            {
                Log.Information("Optimizing general Redis configuration");

                // Set database count
                await _processManager.ExecuteRedisCliCommandAsync($"CONFIG SET databases {_settings.DatabaseCount}");

                // Optimize hash settings for better memory usage
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET hash-max-ziplist-entries 512");
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET hash-max-ziplist-value 64");

                // Optimize list settings
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET list-max-ziplist-size -2");
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET list-compress-depth 0");

                // Optimize set settings
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET set-max-intset-entries 512");

                // Optimize sorted set settings
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET zset-max-ziplist-entries 128");
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET zset-max-ziplist-value 64");

                // Enable lazy freeing for better performance
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET lazyfree-lazy-eviction yes");
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET lazyfree-lazy-expire yes");
                await _processManager.ExecuteRedisCliCommandAsync("CONFIG SET lazyfree-lazy-server-del yes");

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing general configuration: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> PerformCleanupAsync()
        {
            try
            {
                Log.Information("Performing Redis cleanup operations");

                // Clean up expired keys
                await _processManager.ExecuteRedisCliCommandAsync("EVAL \"local keys = redis.call('keys', ARGV[1]) for i=1,#keys,5000 do redis.call('del', unpack(keys, i, math.min(i+4999, #keys))) end return #keys\" 0 *");

                // Defragment memory if supported
                var defragResult = await _processManager.ExecuteRedisCliCommandAsync("MEMORY DOCTOR");
                if (!string.IsNullOrEmpty(defragResult) && defragResult.Contains("fragmentation"))
                {
                    await _processManager.ExecuteRedisCliCommandAsync("MEMORY PURGE");
                    Log.Information("Performed memory defragmentation");
                }

                // Clean up old log files if configured
                await CleanupOldLogFilesAsync();

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error during cleanup: {Error}", ex.Message);
                return false;
            }
        }

        private Task CleanupOldLogFilesAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_settings.LogDirectory) || !Directory.Exists(_settings.LogDirectory))
                    return Task.CompletedTask;

                var logFiles = Directory.GetFiles(_settings.LogDirectory, "redis*.log");
                var cutoffDate = DateTime.Now.AddDays(-_settings.BackupRetentionDays);

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                        Log.Information("Deleted old log file: {LogFile}", logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Error cleaning up old log files: {Error}", ex.Message);
            }

            return Task.CompletedTask;
        }

        public async Task<bool> CreateBackupAsync()
        {
            try
            {
                if (!_settings.EnableAutoBackup)
                    return true;

                Log.Information("Creating Redis backup");

                // Ensure backup directory exists
                if (!Directory.Exists(_settings.BackupDirectory))
                {
                    Directory.CreateDirectory(_settings.BackupDirectory);
                }

                // Trigger a background save
                var saveResult = await _processManager.ExecuteRedisCliCommandAsync("BGSAVE");
                if (string.IsNullOrEmpty(saveResult) || saveResult.Contains("ERR"))
                {
                    Log.Error("Failed to trigger background save: {Result}", saveResult);
                    return false;
                }

                // Wait for save to complete
                await Task.Delay(5000);

                // Copy the dump file to backup directory
                var dumpFile = Path.Combine(_settings.DataDirectory, "dump.rdb");
                if (File.Exists(dumpFile))
                {
                    var backupFile = Path.Combine(_settings.BackupDirectory, $"redis_backup_{DateTime.Now:yyyyMMdd_HHmmss}.rdb");
                    File.Copy(dumpFile, backupFile);
                    Log.Information("Created backup: {BackupFile}", backupFile);

                    // Clean up old backups
                    await CleanupOldBackupsAsync();
                    return true;
                }
                else
                {
                    Log.Warning("Dump file not found for backup: {DumpFile}", dumpFile);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error creating backup: {Error}", ex.Message);
                return false;
            }
        }

        private Task CleanupOldBackupsAsync()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_settings.BackupDirectory, "redis_backup_*.rdb");
                var cutoffDate = DateTime.Now.AddDays(-_settings.BackupRetentionDays);

                foreach (var backupFile in backupFiles)
                {
                    var fileInfo = new FileInfo(backupFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(backupFile);
                        Log.Information("Deleted old backup: {BackupFile}", backupFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Error cleaning up old backups: {Error}", ex.Message);
            }

            return Task.CompletedTask;
        }

        public DateTime LastOptimization => _lastOptimization;
    }
}
