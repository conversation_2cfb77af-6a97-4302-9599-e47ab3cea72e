using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;

namespace RunRedis
{
    /// <summary>
    /// 测试程序，用于验证Redis管理服务的功能
    /// </summary>
    public class TestProgram
    {
        public static async Task TestMain(string[] args)
        {
            // 配置测试日志
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .MinimumLevel.Information()
                .CreateLogger();

            try
            {
                Log.Information("Starting Redis Management Service Test");

                // 加载配置
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                var settings = new RedisSetting();
                configuration.GetSection("RedisSetting").Bind(settings);

                Log.Information("Configuration loaded successfully");
                Log.Information("Redis Host: {Host}:{Port}", settings.Host, settings.Port);

                // 测试各个组件
                await TestRedisProcessManager(settings);
                await TestRedisHealthMonitor(settings);
                await TestEnhancedWorker(settings);

                Log.Information("All tests completed successfully!");
            }
            catch (Exception ex)
            {
                Log.Error("Test failed: {Error}", ex.Message);
                throw;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static async Task TestRedisProcessManager(RedisSetting settings)
        {
            Log.Information("Testing RedisProcessManager...");

            var processManager = new Services.RedisProcessManager(settings);

            try
            {
                // 测试Redis状态检查
                var isRunning = processManager.IsRedisRunning();
                Log.Information("Redis running status: {IsRunning}", isRunning);

                // 如果Redis没有运行，尝试启动
                if (!isRunning)
                {
                    Log.Information("Attempting to start Redis...");
                    var startResult = await processManager.StartRedisAsync();
                    Log.Information("Redis start result: {StartResult}", startResult);
                }

                // 测试Redis CLI命令
                var infoResult = await processManager.ExecuteRedisCliCommandAsync("INFO server");
                if (!string.IsNullOrEmpty(infoResult))
                {
                    Log.Information("Redis INFO command successful");
                }
                else
                {
                    Log.Warning("Redis INFO command failed or returned empty result");
                }

                Log.Information("RedisProcessManager test completed");
            }
            catch (Exception ex)
            {
                Log.Error("RedisProcessManager test failed: {Error}", ex.Message);
            }
            finally
            {
                processManager.Dispose();
            }
        }

        private static async Task TestRedisHealthMonitor(RedisSetting settings)
        {
            Log.Information("Testing RedisHealthMonitor...");

            var processManager = new Services.RedisProcessManager(settings);
            var healthMonitor = new Services.RedisHealthMonitor(settings, processManager);

            try
            {
                // 执行健康检查
                var healthResult = await healthMonitor.PerformHealthCheckAsync();
                
                Log.Information("Health check completed:");
                Log.Information("  - Is Healthy: {IsHealthy}", healthResult.IsHealthy);
                Log.Information("  - Process Running: {ProcessRunning}", healthResult.ProcessRunning);
                Log.Information("  - Database Connectable: {DatabaseConnectable}", healthResult.DatabaseConnectable);
                Log.Information("  - Memory Usage: {MemoryUsageMB} MB", healthResult.MemoryUsageMB);
                Log.Information("  - Active Connections: {ActiveConnections}", healthResult.ActiveConnections);
                Log.Information("  - CPU Usage: {CpuUsagePercent:F1}%", healthResult.CpuUsagePercent);
                Log.Information("  - Response Time: {ResponseTime} ms", healthResult.ResponseTime.TotalMilliseconds);

                if (healthResult.Issues.Count > 0)
                {
                    Log.Warning("Health issues detected:");
                    foreach (var issue in healthResult.Issues)
                    {
                        Log.Warning("  - {Issue}", issue);
                    }
                }

                // 测试性能指标收集
                var performanceMetrics = await healthMonitor.GetPerformanceMetricsAsync();
                if (performanceMetrics != null)
                {
                    Log.Information("Performance metrics collected successfully");
                }

                Log.Information("RedisHealthMonitor test completed");
            }
            catch (Exception ex)
            {
                Log.Error("RedisHealthMonitor test failed: {Error}", ex.Message);
            }
            finally
            {
                healthMonitor.Dispose();
                processManager.Dispose();
            }
        }

        private static async Task TestEnhancedWorker(RedisSetting settings)
        {
            Log.Information("Testing EnhancedWorker for 30 seconds...");

            try
            {
                var worker = new EnhancedWorker(settings);

                Log.Information("Starting Enhanced Worker for 30 seconds...");
                
                // 在后台启动worker
                var workerTask = Task.Run(async () => await worker.ExecuteAsync());
                
                // 让它运行30秒
                await Task.Delay(30000);
                
                // 停止worker
                worker.Stop();
                
                // 等待worker停止（带超时）
                var completedTask = await Task.WhenAny(workerTask, Task.Delay(10000));
                
                if (completedTask == workerTask)
                {
                    Log.Information("Enhanced Worker stopped gracefully");
                }
                else
                {
                    Log.Warning("Enhanced Worker did not stop within timeout");
                }

                Log.Information("EnhancedWorker test completed");
            }
            catch (Exception ex)
            {
                Log.Error("EnhancedWorker test failed: {Error}", ex.Message);
            }
        }
    }
}
