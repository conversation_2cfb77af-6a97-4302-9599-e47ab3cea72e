using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBProcessManager
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoDBConfigurationService _configService;
        private readonly MongoDBPathConfiguration _pathConfig;
        private Process _mongoProcess;
        private int _restartAttempts = 0;

        public MongoDBProcessManager(MongoDBSettings settings)
        {
            _settings = settings;
            _configService = new MongoDBConfigurationService(settings);
            _pathConfig = _configService.ResolveConfigurationPaths();
        }

        public bool IsMongoDBRunning()
        {
            try
            {
                var processes = Process.GetProcessesByName("mongod");
                return processes.Length > 0;
            }
            catch (Exception ex)
            {
                Log.Error("Error checking MongoDB process status: {Error}", ex.Message);
                return false;
            }
        }

        public async Task<bool> StartMongoDBAsync()
        {
            try
            {
                if (IsMongoDBRunning())
                {
                    Log.Information("MongoDB is already running");
                    return true;
                }

                // Prepare for startup
                await PrepareForStartupAsync();

                // Create process start info
                var startInfo = new ProcessStartInfo
                {
                    FileName = _pathConfig.MongoDBExecutablePath,
                    WorkingDirectory = Path.GetDirectoryName(_pathConfig.MongoDBExecutablePath),
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                // Add configuration file if exists
                if (!string.IsNullOrEmpty(_pathConfig.MongoDBConfigPath) && File.Exists(_pathConfig.MongoDBConfigPath))
                {
                    startInfo.ArgumentList.Add("--config");
                    startInfo.ArgumentList.Add(_pathConfig.MongoDBConfigPath);
                    Log.Information("Using MongoDB config file: {ConfigPath}", _pathConfig.MongoDBConfigPath);
                }
                else
                {
                    // Use command line arguments if no config file
                    startInfo.ArgumentList.Add("--dbpath");
                    startInfo.ArgumentList.Add(_pathConfig.DataDirectory);
                    startInfo.ArgumentList.Add("--port");
                    startInfo.ArgumentList.Add(_settings.Port);
                    startInfo.ArgumentList.Add("--logpath");
                    startInfo.ArgumentList.Add(Path.Combine(_pathConfig.LogDirectory, "mongod.log"));
                    startInfo.ArgumentList.Add("--logappend");
                    Log.Information("Using command line arguments for MongoDB configuration");
                }

                _mongoProcess = Process.Start(startInfo);
                
                if (_mongoProcess != null)
                {
                    Log.Information("MongoDB process started with PID: {ProcessId}", _mongoProcess.Id);
                    
                    // Wait a moment for the process to initialize
                    await Task.Delay(5000);
                    
                    // Verify the process is still running
                    if (!_mongoProcess.HasExited)
                    {
                        _restartAttempts = 0; // Reset restart attempts on successful start
                        Log.Information("MongoDB started successfully");
                        return true;
                    }
                    else
                    {
                        Log.Error("MongoDB process exited immediately after start");
                        return false;
                    }
                }
                else
                {
                    Log.Error("Failed to start MongoDB process");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error starting MongoDB: {Error}", ex.Message);
                return false;
            }
        }

        public async Task<bool> StopMongoDBAsync(bool graceful = true)
        {
            try
            {
                var processes = Process.GetProcessesByName("mongod");
                
                if (processes.Length == 0)
                {
                    Log.Information("No MongoDB processes found to stop");
                    return true;
                }

                foreach (var process in processes)
                {
                    try
                    {
                        if (graceful)
                        {
                            // Try graceful shutdown first
                            Log.Information("Attempting graceful shutdown of MongoDB process {ProcessId}", process.Id);
                            process.CloseMainWindow();
                            
                            // Wait for graceful shutdown
                            if (await WaitForProcessExitAsync(process, 30000))
                            {
                                Log.Information("MongoDB process {ProcessId} shut down gracefully", process.Id);
                                continue;
                            }
                            else
                            {
                                Log.Warning("Graceful shutdown timed out for process {ProcessId}, forcing termination", process.Id);
                            }
                        }

                        // Force kill if graceful shutdown failed or not requested
                        process.Kill(true);
                        Log.Information("MongoDB process {ProcessId} terminated", process.Id);
                    }
                    catch (Exception ex)
                    {
                        Log.Error("Error stopping MongoDB process {ProcessId}: {Error}", process.Id, ex.Message);
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error stopping MongoDB: {Error}", ex.Message);
                return false;
            }
        }

        public async Task<bool> RestartMongoDBAsync()
        {
            if (_restartAttempts >= _settings.MaxRestartAttempts)
            {
                Log.Error("Maximum restart attempts ({MaxAttempts}) reached. Manual intervention required.", _settings.MaxRestartAttempts);
                return false;
            }

            _restartAttempts++;
            Log.Information("Attempting MongoDB restart (attempt {Attempt}/{MaxAttempts})", _restartAttempts, _settings.MaxRestartAttempts);

            // Stop MongoDB
            await StopMongoDBAsync(graceful: true);
            
            // Wait before restart
            await Task.Delay(_settings.RestartDelaySeconds * 1000);
            
            // Start MongoDB
            return await StartMongoDBAsync();
        }

        private async Task PrepareForStartupAsync()
        {
            try
            {
                // Ensure directories exist
                EnsureDirectoriesExist();
                
                // Clean up lock files and diagnostic data
                await CleanupLockFilesAsync();
                
                // Clean up old log files if needed
                CleanupOldLogFiles();
                
                Log.Information("MongoDB startup preparation completed");
            }
            catch (Exception ex)
            {
                Log.Error("Error during MongoDB startup preparation: {Error}", ex.Message);
                throw;
            }
        }

        private void EnsureDirectoriesExist()
        {
            var directories = new[]
            {
                _pathConfig.DataDirectory,
                _pathConfig.LogDirectory,
                _pathConfig.BackupDirectory
            };

            foreach (var directory in directories.Where(d => !string.IsNullOrEmpty(d)))
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    Log.Information("Created directory: {Directory}", directory);
                }
            }
        }

        private async Task CleanupLockFilesAsync()
        {
            try
            {
                // Remove mongod.lock file
                var lockFile = Path.Combine(_pathConfig.DataDirectory, "mongod.lock");
                if (File.Exists(lockFile))
                {
                    File.Delete(lockFile);
                    Log.Information("Removed MongoDB lock file: {LockFile}", lockFile);
                }

                // Clean diagnostic data directory
                var diagnosticPath = Path.Combine(_pathConfig.DataDirectory, "diagnostic.data");
                if (Directory.Exists(diagnosticPath))
                {
                    var files = Directory.GetFiles(diagnosticPath);
                    foreach (var file in files)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning("Could not delete diagnostic file {File}: {Error}", file, ex.Message);
                        }
                    }
                    Log.Information("Cleaned diagnostic data directory");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error cleaning up lock files: {Error}", ex.Message);
            }
        }

        private void CleanupOldLogFiles()
        {
            try
            {
                var logFile = Path.Combine(_pathConfig.LogDirectory, "mongod.log");
                if (File.Exists(logFile))
                {
                    var fileInfo = new FileInfo(logFile);
                    // If log file is larger than 100MB, archive it
                    if (fileInfo.Length > 100 * 1024 * 1024)
                    {
                        var archiveName = $"mongod_{DateTime.Now:yyyyMMdd_HHmmss}.log";
                        var archivePath = Path.Combine(_pathConfig.LogDirectory, "archive", archiveName);

                        Directory.CreateDirectory(Path.GetDirectoryName(archivePath));
                        File.Move(logFile, archivePath);
                        Log.Information("Archived large log file to: {ArchivePath}", archivePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Error cleaning up old log files: {Error}", ex.Message);
            }
        }

        private async Task<bool> WaitForProcessExitAsync(Process process, int timeoutMs)
        {
            try
            {
                return await Task.Run(() => process.WaitForExit(timeoutMs));
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _mongoProcess?.Dispose();
        }
    }
}
