using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;
using RunRedis.Services;
using RunRedis.Models;

namespace RunRedis
{
    public class EnhancedWorker
    {
        private readonly RedisSetting _settings;
        private readonly RedisProcessManager _processManager;
        private readonly RedisHealthMonitor _healthMonitor;
        private readonly RedisPerformanceOptimizer _performanceOptimizer;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private bool _isRunning = false;

        public EnhancedWorker(RedisSetting settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _processManager = new RedisProcessManager(_settings);
            _healthMonitor = new RedisHealthMonitor(_settings, _processManager);
            _performanceOptimizer = new RedisPerformanceOptimizer(_settings, _processManager, _healthMonitor);
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public async Task ExecuteAsync()
        {
            _isRunning = true;
            Log.Information("Starting enhanced Redis monitoring and management service");

            try
            {
                // Initial setup and validation
                await InitialSetupAsync();

                // Start main monitoring loop
                await MonitoringLoopAsync(_cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                Log.Fatal("Critical error in Redis worker: {Error}", ex.Message);
                throw;
            }
            finally
            {
                _isRunning = false;
                Log.Information("Enhanced Redis Worker stopped");
            }
        }

        public void Stop()
        {
            Log.Information("Stopping Enhanced Redis Worker");
            _cancellationTokenSource.Cancel();
        }

        private async Task InitialSetupAsync()
        {
            Log.Information("Performing initial Redis setup and validation");

            try
            {
                // Validate configuration
                ValidateConfiguration();

                // Ensure Redis is running
                if (!_processManager.IsRedisRunning())
                {
                    Log.Information("Redis is not running. Starting Redis server...");
                    var startResult = await _processManager.StartRedisAsync();
                    if (!startResult)
                    {
                        throw new InvalidOperationException("Failed to start Redis server during initial setup");
                    }
                }

                // Perform initial health check
                var healthCheck = await _healthMonitor.PerformHealthCheckAsync();
                if (!healthCheck.IsHealthy)
                {
                    Log.Warning("Initial health check failed. Issues: {Issues}", string.Join(", ", healthCheck.Issues));
                }

                // Perform initial optimization if enabled
                if (_settings.EnablePerformanceOptimization)
                {
                    await _performanceOptimizer.OptimizeRedisAsync(force: true);
                }

                Log.Information("Initial setup completed successfully");
            }
            catch (Exception ex)
            {
                Log.Error("Error during initial setup: {Error}", ex.Message);
                throw;
            }
        }

        private void ValidateConfiguration()
        {
            Log.Information("Validating Redis configuration");

            if (string.IsNullOrEmpty(_settings.Host))
                throw new InvalidOperationException("Redis host is not configured");

            if (string.IsNullOrEmpty(_settings.Port) || !int.TryParse(_settings.Port, out var port) || port <= 0)
                throw new InvalidOperationException("Redis port is not properly configured");

            if (_settings.HealthCheckIntervalSeconds <= 0)
                throw new InvalidOperationException("Health check interval must be positive");

            if (_settings.ProcessCheckIntervalSeconds <= 0)
                throw new InvalidOperationException("Process check interval must be positive");

            Log.Information("Configuration validation passed");
        }

        private async Task MonitoringLoopAsync(CancellationToken cancellationToken)
        {
            Log.Information("Starting Redis monitoring loop");

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if Redis process is running
                    if (!_processManager.IsRedisRunning())
                    {
                        Log.Warning("Redis process not detected. Attempting to restart...");
                        await HandleRedisCrashAsync();
                    }
                    else
                    {
                        // Perform health check
                        await PerformHealthCheckAsync();

                        // Perform performance optimization if needed
                        await PerformPerformanceOptimizationAsync();

                        // Perform backup if needed
                        await PerformBackupAsync();
                    }

                    // Wait before next check
                    await Task.Delay(_settings.ProcessCheckIntervalSeconds * 1000, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    Log.Information("Redis monitoring loop cancelled");
                    break;
                }
                catch (Exception ex)
                {
                    Log.Error("Error in monitoring loop: {Error}", ex.Message);
                    await Task.Delay(5000, cancellationToken); // Wait longer on error
                }
            }

            Log.Information("Redis monitoring loop stopped");
        }

        private async Task HandleRedisCrashAsync()
        {
            try
            {
                Log.Warning("Handling Redis crash/unavailability");

                // Attempt to restart Redis
                var restartResult = await _processManager.RestartRedisAsync();

                if (restartResult)
                {
                    Log.Information("Redis restarted successfully after crash");

                    // Perform health check after restart
                    await Task.Delay(5000); // Wait for Redis to fully initialize
                    var healthCheck = await _healthMonitor.PerformHealthCheckAsync();

                    if (healthCheck.IsHealthy)
                    {
                        Log.Information("Redis is healthy after restart");
                    }
                    else
                    {
                        Log.Warning("Redis health check failed after restart. Issues: {Issues}",
                            string.Join(", ", healthCheck.Issues));
                    }
                }
                else
                {
                    Log.Error("Failed to restart Redis. Manual intervention may be required.");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling Redis crash: {Error}", ex.Message);
            }
        }

        private async Task PerformHealthCheckAsync()
        {
            try
            {
                var timeSinceLastCheck = DateTime.Now - _healthMonitor.LastHealthCheck;
                if (timeSinceLastCheck.TotalSeconds >= _settings.HealthCheckIntervalSeconds)
                {
                    var healthCheck = await _healthMonitor.PerformHealthCheckAsync();

                    if (!healthCheck.IsHealthy)
                    {
                        Log.Warning("Redis health check failed. Taking corrective actions...");
                        await HandleHealthIssuesAsync(healthCheck);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during health check: {Error}", ex.Message);
            }
        }

        private async Task HandleHealthIssuesAsync(HealthCheckResult healthCheck)
        {
            try
            {
                foreach (var issue in healthCheck.Issues)
                {
                    Log.Warning("Health issue detected: {Issue}", issue);

                    // Handle specific issues
                    if (issue.Contains("High memory usage"))
                    {
                        Log.Information("Triggering memory optimization due to high usage");
                        await _performanceOptimizer.OptimizeRedisAsync(force: true);
                    }
                    else if (issue.Contains("High connection count"))
                    {
                        Log.Information("High connection count detected, optimizing connection settings");
                        await _performanceOptimizer.OptimizeRedisAsync(force: true);
                    }
                    else if (issue.Contains("High CPU usage"))
                    {
                        Log.Information("High CPU usage detected, checking for optimization opportunities");
                        await _performanceOptimizer.OptimizeRedisAsync(force: true);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling health issues: {Error}", ex.Message);
            }
        }

        private async Task PerformPerformanceOptimizationAsync()
        {
            try
            {
                if (_settings.EnablePerformanceOptimization)
                {
                    await _performanceOptimizer.OptimizeRedisAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during performance optimization: {Error}", ex.Message);
            }
        }

        private async Task PerformBackupAsync()
        {
            try
            {
                if (_settings.EnableAutoBackup)
                {
                    // Check if it's time for a backup (daily backup)
                    var lastOptimization = _performanceOptimizer.LastOptimization;
                    var timeSinceLastBackup = DateTime.Now - lastOptimization;

                    if (timeSinceLastBackup.TotalHours >= 24) // Daily backup
                    {
                        await _performanceOptimizer.CreateBackupAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during backup: {Error}", ex.Message);
            }
        }

        public bool IsRunning => _isRunning;

        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            _processManager?.Dispose();
            _healthMonitor?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}
