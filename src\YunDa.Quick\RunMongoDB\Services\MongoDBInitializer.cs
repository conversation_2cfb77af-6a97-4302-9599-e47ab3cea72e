using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBInitializer
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoClient _mongoClient;

        public MongoDBInitializer(MongoDBSettings settings)
        {
            _settings = settings;
            
            var clientSettings = new MongoClientSettings
            {
                Server = new MongoServerAddress(_settings.Host, int.Parse(_settings.Port)),
                ConnectTimeout = TimeSpan.FromSeconds(_settings.ConnectionTimeoutSeconds),
                ServerSelectionTimeout = TimeSpan.FromSeconds(30)
            };

            if (_settings.IsAuth == "true")
            {
                clientSettings.Credential = MongoCredential.CreateCredential(
                    "admin", _settings.UserName, _settings.PassWord);
            }

            _mongoClient = new MongoClient(clientSettings);
        }

        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                Log.Information("Starting MongoDB database initialization");

                // Wait for MongoDB to be ready
                if (!await WaitForMongoDBReadyAsync())
                {
                    Log.Error("MongoDB is not ready for initialization");
                    return false;
                }

                // Create database if it doesn't exist
                await CreateDatabaseAsync();

                // Create users if authentication is enabled
                if (_settings.IsAuth == "true")
                {
                    await CreateUsersAsync();
                }

                // Create collections and indexes
                await CreateCollectionsAndIndexesAsync();

                // Validate database setup
                var isValid = await ValidateDatabaseSetupAsync();

                if (isValid)
                {
                    Log.Information("MongoDB database initialization completed successfully");
                }
                else
                {
                    Log.Error("MongoDB database initialization validation failed");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                Log.Error("Error during MongoDB database initialization: {Error}", ex.Message);
                return false;
            }
        }

        private async Task<bool> WaitForMongoDBReadyAsync(int maxWaitSeconds = 60)
        {
            var startTime = DateTime.Now;
            
            while ((DateTime.Now - startTime).TotalSeconds < maxWaitSeconds)
            {
                try
                {
                    var database = _mongoClient.GetDatabase("admin");
                    var result = await database.RunCommandAsync<BsonDocument>(new BsonDocument("ping", 1));
                    
                    if (result != null && result.Contains("ok") && result["ok"].ToDouble() == 1.0)
                    {
                        Log.Information("MongoDB is ready for initialization");
                        return true;
                    }
                }
                catch
                {
                    // MongoDB not ready yet, continue waiting
                }

                await Task.Delay(2000); // Wait 2 seconds before retry
            }

            Log.Error("Timeout waiting for MongoDB to become ready");
            return false;
        }

        private async Task CreateDatabaseAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase(_settings.DatabaseName);
                
                // Create a dummy collection to ensure database exists
                var tempCollection = database.GetCollection<BsonDocument>("_temp_init");
                await tempCollection.InsertOneAsync(new BsonDocument("init", true));
                await tempCollection.DeleteOneAsync(new BsonDocument("init", true));
                
                Log.Information("Database '{DatabaseName}' created/verified", _settings.DatabaseName);
            }
            catch (Exception ex)
            {
                Log.Error("Error creating database: {Error}", ex.Message);
                throw;
            }
        }

        private async Task CreateUsersAsync()
        {
            try
            {
                var adminDatabase = _mongoClient.GetDatabase("admin");
                
                // Check if user already exists
                var userExists = await CheckUserExistsAsync(adminDatabase, _settings.UserName);
                
                if (!userExists)
                {
                    // Create admin user
                    var createUserCommand = new BsonDocument
                    {
                        { "createUser", _settings.UserName },
                        { "pwd", _settings.PassWord },
                        { "roles", new BsonArray
                            {
                                new BsonDocument { { "role", "dbAdminAnyDatabase" }, { "db", "admin" } },
                                new BsonDocument { { "role", "readWriteAnyDatabase" }, { "db", "admin" } },
                                new BsonDocument { { "role", "userAdminAnyDatabase" }, { "db", "admin" } }
                            }
                        }
                    };

                    await adminDatabase.RunCommandAsync<BsonDocument>(createUserCommand);
                    Log.Information("Created admin user: {UserName}", _settings.UserName);
                }
                else
                {
                    Log.Information("Admin user already exists: {UserName}", _settings.UserName);
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error creating users: {Error}", ex.Message);
                throw;
            }
        }

        private async Task<bool> CheckUserExistsAsync(IMongoDatabase database, string username)
        {
            try
            {
                var command = new BsonDocument("usersInfo", username);
                var result = await database.RunCommandAsync<BsonDocument>(command);
                
                if (result.Contains("users") && result["users"].IsBsonArray)
                {
                    var users = result["users"].AsBsonArray;
                    return users.Count > 0;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        private async Task CreateCollectionsAndIndexesAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase(_settings.DatabaseName);
                
                // Define collections with their indexes
                var collectionsConfig = new Dictionary<string, List<IndexDefinition>>
                {
                    ["TelemeteringResult"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("DeviceId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) },
                        new IndexDefinition { Keys = new BsonDocument { { "DeviceId", 1 }, { "Timestamp", -1 } } }
                    },
                    ["TelesignalisationResult"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("DeviceId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) },
                        new IndexDefinition { Keys = new BsonDocument { { "DeviceId", 1 }, { "Timestamp", -1 } } }
                    },
                    ["TelecommandResult"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("DeviceId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) },
                        new IndexDefinition { Keys = new BsonDocument("UserId", 1) }
                    },
                    ["InspectionResult"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("InspectionId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) },
                        new IndexDefinition { Keys = new BsonDocument("Status", 1) }
                    },
                    ["InspectionItemResult"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("InspectionId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("ItemId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) }
                    },
                    ["SysAuditLog"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("UserId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) },
                        new IndexDefinition { Keys = new BsonDocument("Action", 1) }
                    },
                    ["MeasureTemperatureResult"] = new List<IndexDefinition>
                    {
                        new IndexDefinition { Keys = new BsonDocument("DeviceId", 1) },
                        new IndexDefinition { Keys = new BsonDocument("Timestamp", -1) },
                        new IndexDefinition { Keys = new BsonDocument { { "DeviceId", 1 }, { "Timestamp", -1 } } }
                    }
                };

                foreach (var collectionConfig in collectionsConfig)
                {
                    await CreateCollectionWithIndexesAsync(database, collectionConfig.Key, collectionConfig.Value);
                }

                Log.Information("Collections and indexes created successfully");
            }
            catch (Exception ex)
            {
                Log.Error("Error creating collections and indexes: {Error}", ex.Message);
                throw;
            }
        }

        private async Task CreateCollectionWithIndexesAsync(IMongoDatabase database, string collectionName, List<IndexDefinition> indexes)
        {
            try
            {
                // Check if collection exists
                var collectionExists = await CollectionExistsAsync(database, collectionName);
                
                if (!collectionExists)
                {
                    // Create collection
                    await database.CreateCollectionAsync(collectionName);
                    Log.Information("Created collection: {CollectionName}", collectionName);
                }

                // Create indexes
                var collection = database.GetCollection<BsonDocument>(collectionName);
                var existingIndexes = await GetExistingIndexesAsync(collection);

                foreach (var indexDef in indexes)
                {
                    var indexName = GenerateIndexName(indexDef.Keys);
                    
                    if (!existingIndexes.Contains(indexName))
                    {
                        var indexModel = new CreateIndexModel<BsonDocument>(indexDef.Keys, new CreateIndexOptions { Name = indexName });
                        await collection.Indexes.CreateOneAsync(indexModel);
                        Log.Information("Created index '{IndexName}' on collection '{CollectionName}'", indexName, collectionName);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error creating collection '{CollectionName}': {Error}", collectionName, ex.Message);
                throw;
            }
        }

        private async Task<bool> CollectionExistsAsync(IMongoDatabase database, string collectionName)
        {
            try
            {
                var options = new ListCollectionsOptions
                {
                    Filter = Builders<BsonDocument>.Filter.Eq("name", collectionName)
                };
                var collections = await database.ListCollectionsAsync(options);
                return await collections.AnyAsync();
            }
            catch
            {
                return false;
            }
        }

        private async Task<HashSet<string>> GetExistingIndexesAsync(IMongoCollection<BsonDocument> collection)
        {
            try
            {
                var indexes = new HashSet<string>();
                var cursor = await collection.Indexes.ListAsync();
                var indexDocuments = await cursor.ToListAsync();
                
                foreach (var indexDoc in indexDocuments)
                {
                    if (indexDoc.Contains("name"))
                    {
                        indexes.Add(indexDoc["name"].AsString);
                    }
                }
                
                return indexes;
            }
            catch
            {
                return new HashSet<string>();
            }
        }

        private string GenerateIndexName(BsonDocument keys)
        {
            var parts = new List<string>();
            foreach (var element in keys.Elements)
            {
                parts.Add($"{element.Name}_{element.Value}");
            }
            return string.Join("_", parts);
        }

        private async Task<bool> ValidateDatabaseSetupAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase(_settings.DatabaseName);
                
                // Check if database is accessible
                var collections = await database.ListCollectionNamesAsync();
                var collectionList = await collections.ToListAsync();
                
                Log.Information("Database validation completed. Found {CollectionCount} collections", collectionList.Count);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Database validation failed: {Error}", ex.Message);
                return false;
            }
        }
    }

    public class IndexDefinition
    {
        public BsonDocument Keys { get; set; }
        public string Name { get; set; }
    }
}
