using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.IO;
using System.Runtime;
using System.Threading.Tasks;

namespace RunMongoDB
{
    public class EnhancedProgram
    {
        public static async Task Main(string[] args)
        {
            // Configure enhanced Serilog with structured logging
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                .WriteTo.File(
                    path: @"D:\SOMS\Logs\RunMongoDB-.txt", 
                    rollingInterval: RollingInterval.Day, 
                    retainedFileCountLimit: 30,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                .MinimumLevel.Information()
                .Enrich.WithProperty("Application", "RunMongoDB")
                .CreateLogger();

            try
            {
                Log.Information("=== Enhanced MongoDB Management Service Starting ===");
                Log.Information("Version: 2.0 - Enhanced with crash recovery, performance optimization, and health monitoring");
                
                // Configure GC for server workloads
                var isServerGC = GCSettings.IsServerGC;
                Log.Information("Server GC Enabled: {IsServerGC}", isServerGC);
                
                if (!isServerGC)
                {
                    Log.Warning("Server GC is not enabled. Consider enabling it for better performance in server environments.");
                }

                // Load configuration
                Log.Information("Loading configuration from: {Directory}", Directory.GetCurrentDirectory());
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();

                // Bind configuration to settings
                var mongoDBSettings = configuration.GetSection("MongoDBSetting").Get<MongoDBSettings>();
                
                if (mongoDBSettings == null)
                {
                    Log.Fatal("Failed to load MongoDB settings from configuration");
                    return;
                }

                Log.Information("MongoDB Configuration loaded:");
                Log.Information("  Host: {Host}:{Port}", mongoDBSettings.Host, mongoDBSettings.Port);
                Log.Information("  Database: {DatabaseName}", mongoDBSettings.DatabaseName);
                Log.Information("  Authentication: {IsAuth}", mongoDBSettings.IsAuth);
                Log.Information("  Data Directory: {DataDirectory}", mongoDBSettings.DataDirectory);
                Log.Information("  Health Check Interval: {HealthCheckInterval}s", mongoDBSettings.HealthCheckIntervalSeconds);
                Log.Information("  Performance Optimization: {EnableOptimization}", mongoDBSettings.EnablePerformanceOptimization);

                // Create and start enhanced worker
                var enhancedWorker = new EnhancedWorker(mongoDBSettings);
                
                // Setup graceful shutdown
                Console.CancelKeyPress += (sender, e) =>
                {
                    e.Cancel = true;
                    Log.Information("Shutdown signal received. Stopping MongoDB management service...");
                    enhancedWorker.Stop();
                };

                // Start the enhanced worker
                await enhancedWorker.ExecuteAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Fatal error occurred during MongoDB management service execution");
                throw;
            }
            finally
            {
                Log.Information("=== Enhanced MongoDB Management Service Stopped ===");
                Log.CloseAndFlush();
            }
        }
    }
}
