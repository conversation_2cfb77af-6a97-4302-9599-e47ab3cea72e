# Enhanced MongoDB Management Service

## Overview

The Enhanced MongoDB Management Service is a comprehensive solution for managing MongoDB instances with advanced features including crash recovery, performance optimization, health monitoring, and automated maintenance.

## Features

### 1. Initial Setup & Database Initialization
- **Automatic database creation** with proper configuration validation
- **Index creation** for optimal query performance on key collections
- **User authentication setup** when security is enabled
- **Data directory and log directory management**
- **Initial data seeding** capabilities

### 2. Crash Recovery & Process Management
- **Automatic crash detection** through process monitoring
- **Intelligent restart procedures** with proper cleanup
- **Data integrity checks** before and after restarts
- **Graceful shutdown** with configurable timeout
- **Lock file and diagnostic data cleanup**
- **Maximum restart attempt limits** to prevent infinite loops

### 3. Performance Optimization
- **Memory usage monitoring** and optimization
- **Connection pool management** with configurable limits
- **Query performance monitoring** and optimization
- **Automatic database compaction** and reindexing
- **Resource allocation tuning** based on system capabilities
- **Scheduled optimization** with configurable intervals

### 4. Health Monitoring & Alerting
- **Comprehensive health checks** including:
  - Process status monitoring
  - Database connectivity verification
  - Memory usage tracking
  - CPU usage monitoring
  - Disk space monitoring
  - Connection count tracking
  - Replication lag detection (if applicable)
- **Configurable health check intervals**
- **Proactive alerting** for potential issues
- **Email notifications** for critical events (configurable)

### 5. Stability Assurance
- **Continuous monitoring** with configurable check intervals
- **Automatic corrective actions** for common issues
- **Performance threshold monitoring** with alerts
- **Backup verification** capabilities
- **Detailed logging** with structured output

## Configuration

### Basic MongoDB Settings
```json
{
  "MongoDBSetting": {
    "Host": "127.0.0.1",
    "Port": "37017",
    "DatabaseName": "isas_mongodb",
    "IsAuth": "false",
    "UserName": "isasAdmin",
    "PassWord": "123456"
  }
}
```

### Enhanced Configuration Options

#### File Paths
- `MongoDBExecutablePath`: Path to mongod.exe
- `MongoDBConfigPath`: Path to MongoDB configuration file
- `DataDirectory`: MongoDB data storage directory
- `LogDirectory`: Log file storage directory
- `BackupDirectory`: Backup storage directory

#### Monitoring Settings
- `HealthCheckIntervalSeconds`: How often to perform health checks (default: 30)
- `ProcessCheckIntervalSeconds`: How often to check if MongoDB process is running (default: 3)
- `MaxRestartAttempts`: Maximum restart attempts before manual intervention (default: 3)
- `RestartDelaySeconds`: Delay between restart attempts (default: 10)

#### Performance Settings
- `ConnectionTimeoutSeconds`: Database connection timeout (default: 30)
- `MaxConnectionPoolSize`: Maximum connections in pool (default: 100)
- `MinConnectionPoolSize`: Minimum connections in pool (default: 5)

#### Performance Thresholds
- `MaxMemoryUsageMB`: Memory usage alert threshold (default: 4096MB)
- `MaxActiveConnections`: Active connection alert threshold (default: 80)
- `MaxCpuUsagePercent`: CPU usage alert threshold (default: 85%)

#### Maintenance Settings
- `EnableAutoBackup`: Enable automatic backups (default: true)
- `BackupRetentionDays`: How long to keep backups (default: 7)
- `EnablePerformanceOptimization`: Enable automatic optimization (default: true)
- `OptimizationIntervalHours`: How often to optimize (default: 24)

#### Alert Settings
- `EnableEmailAlerts`: Enable email notifications (default: false)
- `AlertEmailRecipients`: Email addresses for alerts
- `SmtpServer`: SMTP server for email alerts
- `SmtpPort`: SMTP port (default: 587)
- `SmtpUsername`: SMTP authentication username
- `SmtpPassword`: SMTP authentication password

## Usage

### Running the Enhanced Service

1. **Using the Enhanced Program**:
   ```bash
   dotnet run --project RunMongoDB EnhancedProgram.cs
   ```

2. **Using the Enhanced Worker directly**:
   ```csharp
   var settings = configuration.GetSection("MongoDBSetting").Get<MongoDBSettings>();
   var worker = new EnhancedWorker(settings);
   await worker.ExecuteAsync();
   ```

### MongoDB Configuration File

Copy the provided `sample-mongod.cfg` to your MongoDB installation directory and modify the paths as needed:

```yaml
# Storage settings
storage:
  dbPath: "D:\\SOMS\\Data\\MongoDB\\data"
  journal:
    enabled: true

# Network settings
net:
  port: 37017
  bindIp: 127.0.0.1
  maxIncomingConnections: 200

# Logging
systemLog:
  destination: file
  path: "D:\\SOMS\\Logs\\mongod.log"
  logAppend: true
```

## Architecture

### Service Components

1. **MongoDBProcessManager**: Handles MongoDB process lifecycle
2. **MongoDBHealthMonitor**: Performs comprehensive health checks
3. **MongoDBInitializer**: Manages database initialization and setup
4. **MongoDBPerformanceOptimizer**: Handles performance optimization
5. **EnhancedWorker**: Orchestrates all services and monitoring

### Monitoring Flow

```
Start → Initial Setup → Database Initialization → Monitoring Loop
                                                        ↓
Health Check ← Performance Optimization ← Process Check
     ↓
Issue Detection → Corrective Actions → Alert/Log
```

## Logging

The service provides structured logging with multiple levels:

- **Information**: Normal operations and status updates
- **Warning**: Non-critical issues that may need attention
- **Error**: Errors that don't stop the service but need investigation
- **Fatal**: Critical errors that stop the service

Logs are written to both console and file with rotation.

## Troubleshooting

### Common Issues

1. **MongoDB won't start**:
   - Check if data directory exists and has proper permissions
   - Verify MongoDB executable path is correct
   - Check for port conflicts

2. **High memory usage alerts**:
   - Review MongoDB configuration for cache settings
   - Consider increasing memory limits or optimizing queries

3. **Connection pool exhaustion**:
   - Increase MaxConnectionPoolSize
   - Review application connection usage patterns

4. **Frequent restarts**:
   - Check MongoDB logs for underlying issues
   - Verify system resources (disk space, memory)
   - Review performance thresholds

### Manual Intervention

If the service reaches maximum restart attempts:

1. Check MongoDB logs for error details
2. Verify system resources and disk space
3. Check data directory for corruption
4. Consider running MongoDB repair manually
5. Restart the management service after resolving issues

## Performance Recommendations

1. **Memory**: Allocate at least 4GB RAM for MongoDB
2. **Storage**: Use SSD storage for better I/O performance
3. **Network**: Ensure low latency between application and MongoDB
4. **Indexes**: Regularly review and optimize database indexes
5. **Monitoring**: Keep health check intervals reasonable (30-60 seconds)

## Security Considerations

1. **Authentication**: Enable MongoDB authentication in production
2. **Network**: Bind MongoDB to specific interfaces, not 0.0.0.0
3. **Encryption**: Consider enabling encryption at rest and in transit
4. **Firewall**: Restrict MongoDB port access to authorized systems
5. **Logs**: Secure log files and rotate them regularly

## Version History

- **v2.0**: Enhanced MongoDB Management Service with comprehensive monitoring
- **v1.0**: Basic MongoDB process monitoring and restart capabilities
