﻿using System;
using System.Collections.Generic;
using System.Text;

namespace RunMongoDB
{
    using System.Diagnostics;
    using System.Threading.Tasks;

    public static class ProcessExtensions
    {
        public static Task WaitForExitAsync(this Process process)
        {
            if (process.HasExited)
                return Task.CompletedTask;

            var tcs = new TaskCompletionSource<bool>();
            process.EnableRaisingEvents = true;
            process.Exited += (sender, args) => tcs.TrySetResult(true);

            // 确保进程未在检查后立即退出
            if (process.HasExited)
                tcs.TrySetResult(true);

            return tcs.Task;
        }
    }

}
