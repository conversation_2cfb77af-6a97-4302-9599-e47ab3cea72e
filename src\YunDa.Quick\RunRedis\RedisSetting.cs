﻿using System;
using System.Collections.Generic;
using System.Text;

namespace RunRedis
{
    public class RedisSetting
    {
        // Basic connection settings
        public string Host { get; set; } = "127.0.0.1";
        public string Port { get; set; } = "6379";
        public string Auth { get; set; } = "";
        public string Name { get; set; } = "";
        public string ClusterType { get; set; } = "";
        public string DefaultDatabaseIndex { get; set; } = "0";

        // Enhanced configuration properties
        public string RedisExecutablePath { get; set; } = @"redis-server.exe";
        public string RedisConfigPath { get; set; } = @"redis.conf";
        public string RedisCliPath { get; set; } = @"redis-cli.exe";
        public string DataDirectory { get; set; } = @"D:\SOMS\Data\Redis\";
        public string LogDirectory { get; set; } = @"D:\SOMS\Logs\";
        public string BackupDirectory { get; set; } = @"D:\SOMS\Backup\Redis\";

        // Monitoring settings
        public int HealthCheckIntervalSeconds { get; set; } = 30;
        public int ProcessCheckIntervalSeconds { get; set; } = 3;
        public int MaxRestartAttempts { get; set; } = 3;
        public int RestartDelaySeconds { get; set; } = 10;
        public int ConnectionTimeoutSeconds { get; set; } = 30;

        // Performance settings
        public int MaxConnectionPoolSize { get; set; } = 100;
        public int MinConnectionPoolSize { get; set; } = 5;
        public long MaxMemoryUsageMB { get; set; } = 2048;
        public int MaxActiveConnections { get; set; } = 80;
        public double MaxCpuUsagePercent { get; set; } = 85.0;

        // Backup and maintenance settings
        public bool EnableAutoBackup { get; set; } = true;
        public int BackupRetentionDays { get; set; } = 7;
        public bool EnablePerformanceOptimization { get; set; } = true;
        public int OptimizationIntervalHours { get; set; } = 24;

        // Alert settings
        public bool EnableEmailAlerts { get; set; } = false;
        public string AlertEmailRecipients { get; set; } = "";
        public string SmtpServer { get; set; } = "";
        public int SmtpPort { get; set; } = 587;
        public string SmtpUsername { get; set; } = "";
        public string SmtpPassword { get; set; } = "";

        // Advanced Redis settings
        public string MaxMemoryPolicy { get; set; } = "allkeys-lru";
        public bool EnablePersistence { get; set; } = true;
        public string SaveConfiguration { get; set; } = "900 1 300 10 60 10000";
        public int DatabaseCount { get; set; } = 16;
        public int ClientTimeoutSeconds { get; set; } = 300;
        public bool EnableSlowLog { get; set; } = true;
        public int SlowLogMaxLength { get; set; } = 128;
        public int SlowLogSlowerThanMicroseconds { get; set; } = 10000;
    }
}
