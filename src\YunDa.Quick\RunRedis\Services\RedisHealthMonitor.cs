using Serilog;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using RunRedis.Models;

namespace RunRedis.Services
{
    public class RedisHealthMonitor
    {
        private readonly RedisSetting _settings;
        private readonly RedisProcessManager _processManager;
        private IConnectionMultiplexer _redisConnection;
        private bool _isHealthy = false;
        private DateTime _lastHealthCheck = DateTime.MinValue;

        public RedisHealthMonitor(RedisSetting settings, RedisProcessManager processManager)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _processManager = processManager ?? throw new ArgumentNullException(nameof(processManager));
        }

        public bool IsHealthy => _isHealthy;
        public DateTime LastHealthCheck => _lastHealthCheck;

        public async Task<HealthCheckResult> PerformHealthCheckAsync()
        {
            var result = new HealthCheckResult
            {
                CheckTime = DateTime.Now,
                IsHealthy = false
            };

            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Check if Redis process is running
                result.ProcessRunning = IsRedisProcessRunning();
                if (!result.ProcessRunning)
                {
                    result.Issues.Add("Redis process is not running");
                    return result;
                }

                // Check database connectivity
                result.DatabaseConnectable = await CheckDatabaseConnectivityAsync();
                if (!result.DatabaseConnectable)
                {
                    result.Issues.Add("Cannot connect to Redis database");
                    return result;
                }

                // Get server information
                var serverInfo = await GetServerInfoAsync();
                if (serverInfo != null)
                {
                    result.ServerInfo = serverInfo;
                    
                    // Parse server information
                    ParseServerInfo(serverInfo, result);
                    
                    // Check memory usage
                    if (result.MemoryUsageMB > _settings.MaxMemoryUsageMB)
                    {
                        result.Issues.Add($"High memory usage: {result.MemoryUsageMB}MB (threshold: {_settings.MaxMemoryUsageMB}MB)");
                    }

                    // Check active connections
                    if (result.ActiveConnections > _settings.MaxActiveConnections)
                    {
                        result.Issues.Add($"High connection count: {result.ActiveConnections} (threshold: {_settings.MaxActiveConnections})");
                    }

                    // Check CPU usage from process
                    result.CpuUsagePercent = GetProcessCpuUsage();
                    if (result.CpuUsagePercent > _settings.MaxCpuUsagePercent)
                    {
                        result.Issues.Add($"High CPU usage: {result.CpuUsagePercent:F1}% (threshold: {_settings.MaxCpuUsagePercent}%)");
                    }

                    // Check hit ratio
                    if (result.KeyspaceHits + result.KeyspaceMisses > 0)
                    {
                        result.HitRatio = (double)result.KeyspaceHits / (result.KeyspaceHits + result.KeyspaceMisses) * 100;
                        if (result.HitRatio < 80) // Consider hit ratio below 80% as concerning
                        {
                            result.Issues.Add($"Low hit ratio: {result.HitRatio:F1}% (consider optimizing cache usage)");
                        }
                    }

                    // Check slow log
                    await CheckSlowLogAsync(result);
                }

                stopwatch.Stop();
                result.ResponseTime = stopwatch.Elapsed;

                // Overall health assessment
                result.IsHealthy = result.ProcessRunning && 
                                 result.DatabaseConnectable && 
                                 result.Issues.Count == 0;

                _isHealthy = result.IsHealthy;
                _lastHealthCheck = result.CheckTime;

                if (result.IsHealthy)
                {
                    Log.Information("Redis health check passed - Memory: {MemoryMB}MB, Connections: {Connections}, CPU: {CpuUsage:F1}%, Hit Ratio: {HitRatio:F1}%", 
                        result.MemoryUsageMB, result.ActiveConnections, result.CpuUsagePercent, result.HitRatio);
                }
                else
                {
                    Log.Warning("Redis health check failed. Issues: {Issues}", string.Join(", ", result.Issues));
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.ResponseTime = stopwatch.Elapsed;
                result.Issues.Add($"Health check error: {ex.Message}");
                Log.Error("Error during Redis health check: {Error}", ex.Message);
                return result;
            }
        }

        private bool IsRedisProcessRunning()
        {
            try
            {
                var processes = Process.GetProcessesByName("redis-server");
                return processes.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckDatabaseConnectivityAsync()
        {
            try
            {
                await EnsureRedisConnectionAsync();
                if (_redisConnection != null && _redisConnection.IsConnected)
                {
                    var database = _redisConnection.GetDatabase();
                    await database.PingAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Log.Debug("Database connectivity check failed: {Error}", ex.Message);
                return false;
            }
        }

        private async Task EnsureRedisConnectionAsync()
        {
            try
            {
                if (_redisConnection == null || !_redisConnection.IsConnected)
                {
                    var connectionString = BuildConnectionString();
                    _redisConnection = await ConnectionMultiplexer.ConnectAsync(connectionString);
                }
            }
            catch (Exception ex)
            {
                Log.Debug("Error establishing Redis connection: {Error}", ex.Message);
                _redisConnection?.Dispose();
                _redisConnection = null;
            }
        }

        private string BuildConnectionString()
        {
            var options = new List<string>
            {
                $"{_settings.Host}:{_settings.Port}",
                $"defaultDatabase={_settings.DefaultDatabaseIndex}",
                $"connectTimeout={_settings.ConnectionTimeoutSeconds * 1000}",
                $"syncTimeout={_settings.ConnectionTimeoutSeconds * 1000}"
            };

            if (!string.IsNullOrEmpty(_settings.Auth))
            {
                options.Add($"password={_settings.Auth}");
            }

            return string.Join(",", options);
        }

        private async Task<Dictionary<string, object>> GetServerInfoAsync()
        {
            try
            {
                await EnsureRedisConnectionAsync();
                if (_redisConnection?.IsConnected == true)
                {
                    var server = _redisConnection.GetServer($"{_settings.Host}:{_settings.Port}");
                    var info = await server.InfoAsync();
                    
                    var result = new Dictionary<string, object>();
                    
                    foreach (var section in info)
                    {
                        foreach (var item in section)
                        {
                            result[item.Key] = item.Value;
                        }
                    }
                    
                    return result;
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Debug("Error getting server info: {Error}", ex.Message);
                return null;
            }
        }

        private void ParseServerInfo(Dictionary<string, object> serverInfo, HealthCheckResult result)
        {
            try
            {
                // Memory information
                if (serverInfo.TryGetValue("used_memory", out var usedMemory))
                {
                    if (long.TryParse(usedMemory.ToString(), out var usedMemoryBytes))
                    {
                        result.UsedMemoryBytes = usedMemoryBytes;
                        result.MemoryUsageMB = usedMemoryBytes / (1024 * 1024);
                    }
                }

                if (serverInfo.TryGetValue("maxmemory", out var maxMemory))
                {
                    if (long.TryParse(maxMemory.ToString(), out var maxMemoryBytes))
                    {
                        result.MaxMemoryBytes = maxMemoryBytes;
                    }
                }

                // Connection information
                if (serverInfo.TryGetValue("connected_clients", out var connectedClients))
                {
                    if (int.TryParse(connectedClients.ToString(), out var clientCount))
                    {
                        result.ConnectedClients = clientCount;
                        result.ActiveConnections = clientCount;
                    }
                }

                // Command statistics
                if (serverInfo.TryGetValue("total_commands_processed", out var totalCommands))
                {
                    if (int.TryParse(totalCommands.ToString(), out var commandCount))
                    {
                        result.TotalCommandsProcessed = commandCount;
                    }
                }

                // Keyspace statistics
                if (serverInfo.TryGetValue("keyspace_hits", out var hits))
                {
                    if (int.TryParse(hits.ToString(), out var hitCount))
                    {
                        result.KeyspaceHits = hitCount;
                    }
                }

                if (serverInfo.TryGetValue("keyspace_misses", out var misses))
                {
                    if (int.TryParse(misses.ToString(), out var missCount))
                    {
                        result.KeyspaceMisses = missCount;
                    }
                }

                // Server information
                if (serverInfo.TryGetValue("redis_version", out var version))
                {
                    result.RedisVersion = version.ToString();
                }

                if (serverInfo.TryGetValue("uptime_in_seconds", out var uptime))
                {
                    result.UptimeInSeconds = uptime.ToString();
                }

                // Database count
                var dbCount = serverInfo.Keys.Count(k => k.StartsWith("db"));
                result.DatabaseCount = dbCount;

                // Persistence information
                if (serverInfo.TryGetValue("rdb_last_save_time", out var lastSave))
                {
                    if (long.TryParse(lastSave.ToString(), out var lastSaveTimestamp))
                    {
                        result.LastSaveTime = DateTimeOffset.FromUnixTimeSeconds(lastSaveTimestamp).DateTime;
                        result.PersistenceEnabled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Debug("Error parsing server info: {Error}", ex.Message);
            }
        }

        private double GetProcessCpuUsage()
        {
            try
            {
                var processes = Process.GetProcessesByName("redis-server");
                if (processes.Length > 0)
                {
                    var process = processes[0];
                    var totalProcessorTime = process.TotalProcessorTime;
                    var elapsedTime = (DateTime.Now - process.StartTime).TotalSeconds;

                    if (elapsedTime > 0)
                    {
                        return (totalProcessorTime.TotalMilliseconds / (elapsedTime * Environment.ProcessorCount)) * 100;
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task CheckSlowLogAsync(HealthCheckResult result)
        {
            try
            {
                var slowLogOutput = await _processManager.ExecuteRedisCliCommandAsync("SLOWLOG LEN");
                if (!string.IsNullOrEmpty(slowLogOutput) && int.TryParse(slowLogOutput, out var slowLogLength))
                {
                    result.SlowLogLength = slowLogLength;
                    
                    if (slowLogLength > _settings.SlowLogMaxLength * 0.8) // Warning at 80% capacity
                    {
                        result.Issues.Add($"Slow log is getting full: {slowLogLength}/{_settings.SlowLogMaxLength} entries");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Debug("Error checking slow log: {Error}", ex.Message);
            }
        }

        public async Task<RedisPerformanceMetrics> GetPerformanceMetricsAsync()
        {
            try
            {
                var healthCheck = await PerformHealthCheckAsync();
                
                return new RedisPerformanceMetrics
                {
                    Timestamp = DateTime.Now,
                    MemoryUsage = healthCheck.MemoryUsageMB,
                    CpuUsage = healthCheck.CpuUsagePercent,
                    ConnectionCount = healthCheck.ActiveConnections,
                    HitRatio = healthCheck.HitRatio,
                    AverageResponseTime = healthCheck.ResponseTime
                };
            }
            catch (Exception ex)
            {
                Log.Error("Error getting performance metrics: {Error}", ex.Message);
                return null;
            }
        }

        public void Dispose()
        {
            _redisConnection?.Dispose();
        }
    }
}
