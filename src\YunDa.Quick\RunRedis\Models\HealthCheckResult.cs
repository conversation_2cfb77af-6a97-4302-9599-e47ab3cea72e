using System;
using System.Collections.Generic;

namespace RunRedis.Models
{
    public class HealthCheckResult
    {
        public DateTime CheckTime { get; set; }
        public bool IsHealthy { get; set; }
        public bool ProcessRunning { get; set; }
        public bool DatabaseConnectable { get; set; }
        public long MemoryUsageMB { get; set; }
        public int ActiveConnections { get; set; }
        public double CpuUsagePercent { get; set; }
        public long UsedMemoryBytes { get; set; }
        public long MaxMemoryBytes { get; set; }
        public int ConnectedClients { get; set; }
        public int TotalCommandsProcessed { get; set; }
        public double HitRatio { get; set; }
        public int KeyspaceHits { get; set; }
        public int KeyspaceMisses { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
        public Dictionary<string, object> ServerInfo { get; set; } = new Dictionary<string, object>();
        public TimeSpan ResponseTime { get; set; }
        public string RedisVersion { get; set; }
        public string UptimeInSeconds { get; set; }
        public int DatabaseCount { get; set; }
        public bool PersistenceEnabled { get; set; }
        public DateTime LastSaveTime { get; set; }
        public int SlowLogLength { get; set; }
    }

    public class RedisPerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public long MemoryUsage { get; set; }
        public double CpuUsage { get; set; }
        public int ConnectionCount { get; set; }
        public long CommandsPerSecond { get; set; }
        public double HitRatio { get; set; }
        public int KeyCount { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
    }

    public class RedisBackupInfo
    {
        public DateTime BackupTime { get; set; }
        public string BackupPath { get; set; }
        public long BackupSizeBytes { get; set; }
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
    }
}
