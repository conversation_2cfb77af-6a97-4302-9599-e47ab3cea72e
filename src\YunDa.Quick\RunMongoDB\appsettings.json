{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "MongoDBSetting": {"Host": "127.0.0.1", "Port": "37017", "DatabaseName": "isas_mongodb", "IsAuth": "false", "UserName": "isasAdmin", "PassWord": "123456", "MongoDBExecutablePath": "D:\\SOMS\\大数据库\\mongod.exe", "MongoDBConfigPath": "D:\\SOMS\\大数据库\\mongod.cfg", "DataDirectory": "D:\\SOMS\\Data\\MongoDB\\data\\", "LogDirectory": "D:\\SOMS\\Logs\\", "BackupDirectory": "D:\\SOMS\\Backup\\MongoDB\\", "HealthCheckIntervalSeconds": 30, "ProcessCheckIntervalSeconds": 3, "MaxRestartAttempts": 3, "RestartDelaySeconds": 10, "ConnectionTimeoutSeconds": 30, "MaxConnectionPoolSize": 100, "MinConnectionPoolSize": 5, "MaxMemoryUsageMB": 4096, "MaxActiveConnections": 80, "MaxCpuUsagePercent": 85.0, "EnableAutoBackup": true, "BackupRetentionDays": 7, "EnablePerformanceOptimization": true, "OptimizationIntervalHours": 24, "EnableEmailAlerts": false, "AlertEmailRecipients": "", "SmtpServer": "", "SmtpPort": 587, "SmtpUsername": "", "SmtpPassword": ""}}