using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.IO;

namespace RunRedis
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // 配置 Serilog
            Log.Logger = new LoggerConfiguration()
                 .WriteTo.Console() // 输出到控制台
                .MinimumLevel.Information()
                .WriteTo.File(@"D:\SOMS\Logs\RunRedislog-.txt", rollingInterval: RollingInterval.Day, retainedFileCountLimit: 7)
                .CreateLogger();
            Log.Information("内存数据库正在启动");
            Log.Information("当前目录：" + Directory.GetCurrentDirectory());
            var configuration = new ConfigurationBuilder()
                .AddJsonFile(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"), optional: false, reloadOnChange: true)
                .Build();
            var redisSetting = configuration.GetSection("RedisSetting").Get<RedisSetting>();

            Worker worker = new Worker(redisSetting);
            worker.ExecuteAsync().Wait();
        }
    }
}
