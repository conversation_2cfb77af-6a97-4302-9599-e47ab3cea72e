using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace RunRedis
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Configure Serilog early
            ConfigureSerilog();

            try
            {
                Log.Information("Redis Management Service starting up");
                Log.Information("Current directory: {CurrentDirectory}", Directory.GetCurrentDirectory());

                // Create host builder
                var host = CreateHostBuilder(args).Build();

                // Run the service
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal("Application terminated unexpectedly: {Error}", ex.Message);
                throw;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService() // Enable Windows Service support
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.SetBasePath(Directory.GetCurrentDirectory())
                          .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                          .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true, reloadOnChange: true)
                          .AddEnvironmentVariables()
                          .AddCommandLine(args);
                })
                .ConfigureServices((hostContext, services) =>
                {
                    // Configure Redis settings
                    var redisSettings = new RedisSetting();
                    hostContext.Configuration.GetSection("RedisSetting").Bind(redisSettings);
                    services.AddSingleton(redisSettings);

                    // Register the worker service
                    services.AddHostedService<RedisWorkerService>();
                })
                .UseSerilog(); // Use Serilog for logging

        private static void ConfigureSerilog()
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Get log directory from configuration or use default
            var logDirectory = configuration.GetSection("RedisSetting:LogDirectory").Value ?? @"D:\SOMS\Logs\";

            // Ensure log directory exists
            Directory.CreateDirectory(logDirectory);

            var logFilePath = Path.Combine(logDirectory, "RunRedis-.txt");

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .WriteTo.File(
                    logFilePath,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .MinimumLevel.Information()
                .Enrich.FromLogContext()
                .CreateLogger();
        }
    }

    public class RedisWorkerService : BackgroundService
    {
        private readonly RedisSetting _settings;
        private readonly ILogger<RedisWorkerService> _logger;
        private EnhancedWorker _worker;

        public RedisWorkerService(RedisSetting settings, ILogger<RedisWorkerService> logger)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("Redis Worker Service starting");

                _worker = new EnhancedWorker(_settings);

                // Register cancellation callback
                stoppingToken.Register(() =>
                {
                    _logger.LogInformation("Redis Worker Service stopping");
                    _worker?.Stop();
                });

                // Execute the enhanced worker
                await _worker.ExecuteAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Redis Worker Service");
                throw;
            }
        }

        public override void Dispose()
        {
            _worker?.Dispose();
            base.Dispose();
        }
    }
}
