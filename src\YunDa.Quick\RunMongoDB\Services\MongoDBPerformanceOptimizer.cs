using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBPerformanceOptimizer
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoClient _mongoClient;
        private DateTime _lastOptimization = DateTime.MinValue;

        public MongoDBPerformanceOptimizer(MongoDBSettings settings)
        {
            _settings = settings;
            
            var clientSettings = new MongoClientSettings
            {
                Server = new MongoServerAddress(_settings.Host, int.Parse(_settings.Port)),
                ConnectTimeout = TimeSpan.FromSeconds(_settings.ConnectionTimeoutSeconds),
                MaxConnectionPoolSize = _settings.MaxConnectionPoolSize,
                MinConnectionPoolSize = _settings.MinConnectionPoolSize
            };

            if (_settings.IsAuth == "true")
            {
                clientSettings.Credential = MongoCredential.CreateCredential(
                    "admin", _settings.UserName, _settings.PassWord);
            }

            _mongoClient = new MongoClient(clientSettings);
        }

        public async Task<bool> OptimizePerformanceAsync()
        {
            try
            {
                if (!ShouldRunOptimization())
                {
                    return true;
                }

                Log.Information("Starting MongoDB performance optimization");

                var database = _mongoClient.GetDatabase(_settings.DatabaseName);
                
                // Get all collections
                var collections = await database.ListCollectionNamesAsync();
                var collectionList = await collections.ToListAsync();

                var optimizationResults = new List<OptimizationResult>();

                foreach (var collectionName in collectionList)
                {
                    var result = await OptimizeCollectionAsync(database, collectionName);
                    optimizationResults.Add(result);
                }

                // Perform database-level optimizations
                await OptimizeDatabaseAsync(database);

                // Update last optimization time
                _lastOptimization = DateTime.Now;

                // Log results
                var successCount = optimizationResults.Count(r => r.Success);
                Log.Information("Performance optimization completed. {SuccessCount}/{TotalCount} collections optimized successfully", 
                    successCount, optimizationResults.Count);

                return successCount == optimizationResults.Count;
            }
            catch (Exception ex)
            {
                Log.Error("Error during performance optimization: {Error}", ex.Message);
                return false;
            }
        }

        private bool ShouldRunOptimization()
        {
            if (!_settings.EnablePerformanceOptimization)
            {
                return false;
            }

            var timeSinceLastOptimization = DateTime.Now - _lastOptimization;
            return timeSinceLastOptimization.TotalHours >= _settings.OptimizationIntervalHours;
        }

        private async Task<OptimizationResult> OptimizeCollectionAsync(IMongoDatabase database, string collectionName)
        {
            var result = new OptimizationResult
            {
                CollectionName = collectionName,
                StartTime = DateTime.Now
            };

            try
            {
                Log.Information("Optimizing collection: {CollectionName}", collectionName);

                // Get collection statistics before optimization
                var statsBefore = await GetCollectionStatsAsync(database, collectionName);
                result.SizeBefore = statsBefore?.Size ?? 0;
                result.IndexSizeBefore = statsBefore?.TotalIndexSize ?? 0;

                // Perform compact operation to reclaim space
                await CompactCollectionAsync(database, collectionName);

                // Reindex collection for better performance
                await ReindexCollectionAsync(database, collectionName);

                // Get collection statistics after optimization
                var statsAfter = await GetCollectionStatsAsync(database, collectionName);
                result.SizeAfter = statsAfter?.Size ?? 0;
                result.IndexSizeAfter = statsAfter?.TotalIndexSize ?? 0;

                result.SpaceSaved = result.SizeBefore - result.SizeAfter;
                result.Success = true;
                result.EndTime = DateTime.Now;

                Log.Information("Collection '{CollectionName}' optimized. Space saved: {SpaceSaved} bytes", 
                    collectionName, result.SpaceSaved);

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.Now;
                
                Log.Error("Error optimizing collection '{CollectionName}': {Error}", collectionName, ex.Message);
                return result;
            }
        }

        private async Task<CollectionStats> GetCollectionStatsAsync(IMongoDatabase database, string collectionName)
        {
            try
            {
                var command = new BsonDocument("collStats", collectionName);
                var result = await database.RunCommandAsync<BsonDocument>(command);

                return new CollectionStats
                {
                    Size = result.Contains("size") ? result["size"].ToInt64() : 0,
                    StorageSize = result.Contains("storageSize") ? result["storageSize"].ToInt64() : 0,
                    TotalIndexSize = result.Contains("totalIndexSize") ? result["totalIndexSize"].ToInt64() : 0,
                    Count = result.Contains("count") ? result["count"].ToInt64() : 0
                };
            }
            catch
            {
                return null;
            }
        }

        private async Task CompactCollectionAsync(IMongoDatabase database, string collectionName)
        {
            try
            {
                var command = new BsonDocument("compact", collectionName);
                await database.RunCommandAsync<BsonDocument>(command);
                Log.Debug("Compacted collection: {CollectionName}", collectionName);
            }
            catch (Exception ex)
            {
                Log.Warning("Could not compact collection '{CollectionName}': {Error}", collectionName, ex.Message);
            }
        }

        private async Task ReindexCollectionAsync(IMongoDatabase database, string collectionName)
        {
            try
            {
                var command = new BsonDocument("reIndex", collectionName);
                await database.RunCommandAsync<BsonDocument>(command);
                Log.Debug("Reindexed collection: {CollectionName}", collectionName);
            }
            catch (Exception ex)
            {
                Log.Warning("Could not reindex collection '{CollectionName}': {Error}", collectionName, ex.Message);
            }
        }

        private async Task OptimizeDatabaseAsync(IMongoDatabase database)
        {
            try
            {
                Log.Information("Performing database-level optimizations");

                // Run database repair if needed
                await RepairDatabaseAsync(database);

                // Update database statistics
                await UpdateDatabaseStatsAsync(database);

                // Optimize query planner cache
                await OptimizeQueryPlannerAsync(database);

                Log.Information("Database-level optimizations completed");
            }
            catch (Exception ex)
            {
                Log.Error("Error during database-level optimization: {Error}", ex.Message);
            }
        }

        private async Task RepairDatabaseAsync(IMongoDatabase database)
        {
            try
            {
                // Note: repairDatabase is deprecated in newer MongoDB versions
                // This is kept for compatibility with older versions
                var command = new BsonDocument("repairDatabase", 1);
                await database.RunCommandAsync<BsonDocument>(command);
                Log.Debug("Database repair completed");
            }
            catch (Exception ex)
            {
                Log.Debug("Database repair not available or failed: {Error}", ex.Message);
            }
        }

        private async Task UpdateDatabaseStatsAsync(IMongoDatabase database)
        {
            try
            {
                var command = new BsonDocument("dbStats", 1);
                var result = await database.RunCommandAsync<BsonDocument>(command);
                
                if (result != null)
                {
                    var dataSize = result.Contains("dataSize") ? result["dataSize"].ToInt64() : 0;
                    var storageSize = result.Contains("storageSize") ? result["storageSize"].ToInt64() : 0;
                    var indexSize = result.Contains("indexSize") ? result["indexSize"].ToInt64() : 0;
                    
                    Log.Information("Database stats - Data: {DataSize} bytes, Storage: {StorageSize} bytes, Indexes: {IndexSize} bytes",
                        dataSize, storageSize, indexSize);
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Could not update database stats: {Error}", ex.Message);
            }
        }

        private async Task OptimizeQueryPlannerAsync(IMongoDatabase database)
        {
            try
            {
                // Clear query plan cache to ensure optimal query plans
                var command = new BsonDocument("planCacheClear", 1);
                await database.RunCommandAsync<BsonDocument>(command);
                Log.Debug("Query planner cache cleared");
            }
            catch (Exception ex)
            {
                Log.Debug("Could not clear query planner cache: {Error}", ex.Message);
            }
        }

        public async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase("admin");
                var serverStatus = await database.RunCommandAsync<BsonDocument>(new BsonDocument("serverStatus", 1));
                
                var metrics = new PerformanceMetrics
                {
                    Timestamp = DateTime.Now
                };

                if (serverStatus != null)
                {
                    // Extract performance metrics
                    if (serverStatus.Contains("opcounters"))
                    {
                        var opcounters = serverStatus["opcounters"].AsBsonDocument;
                        metrics.InsertOps = opcounters.Contains("insert") ? opcounters["insert"].ToInt64() : 0;
                        metrics.QueryOps = opcounters.Contains("query") ? opcounters["query"].ToInt64() : 0;
                        metrics.UpdateOps = opcounters.Contains("update") ? opcounters["update"].ToInt64() : 0;
                        metrics.DeleteOps = opcounters.Contains("delete") ? opcounters["delete"].ToInt64() : 0;
                    }

                    if (serverStatus.Contains("connections"))
                    {
                        var connections = serverStatus["connections"].AsBsonDocument;
                        metrics.CurrentConnections = connections.Contains("current") ? connections["current"].ToInt32() : 0;
                        metrics.AvailableConnections = connections.Contains("available") ? connections["available"].ToInt32() : 0;
                    }

                    if (serverStatus.Contains("mem"))
                    {
                        var mem = serverStatus["mem"].AsBsonDocument;
                        metrics.ResidentMemoryMB = mem.Contains("resident") ? mem["resident"].ToInt64() : 0;
                        metrics.VirtualMemoryMB = mem.Contains("virtual") ? mem["virtual"].ToInt64() : 0;
                    }

                    if (serverStatus.Contains("globalLock"))
                    {
                        var globalLock = serverStatus["globalLock"].AsBsonDocument;
                        if (globalLock.Contains("currentQueue"))
                        {
                            var queue = globalLock["currentQueue"].AsBsonDocument;
                            metrics.QueuedReaders = queue.Contains("readers") ? queue["readers"].ToInt32() : 0;
                            metrics.QueuedWriters = queue.Contains("writers") ? queue["writers"].ToInt32() : 0;
                        }
                    }
                }

                return metrics;
            }
            catch (Exception ex)
            {
                Log.Error("Error getting performance metrics: {Error}", ex.Message);
                return new PerformanceMetrics { Timestamp = DateTime.Now };
            }
        }

        public DateTime LastOptimization => _lastOptimization;
    }

    public class OptimizationResult
    {
        public string CollectionName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public long SizeBefore { get; set; }
        public long SizeAfter { get; set; }
        public long IndexSizeBefore { get; set; }
        public long IndexSizeAfter { get; set; }
        public long SpaceSaved { get; set; }
    }

    public class CollectionStats
    {
        public long Size { get; set; }
        public long StorageSize { get; set; }
        public long TotalIndexSize { get; set; }
        public long Count { get; set; }
    }

    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public long InsertOps { get; set; }
        public long QueryOps { get; set; }
        public long UpdateOps { get; set; }
        public long DeleteOps { get; set; }
        public int CurrentConnections { get; set; }
        public int AvailableConnections { get; set; }
        public long ResidentMemoryMB { get; set; }
        public long VirtualMemoryMB { get; set; }
        public int QueuedReaders { get; set; }
        public int QueuedWriters { get; set; }
    }
}
