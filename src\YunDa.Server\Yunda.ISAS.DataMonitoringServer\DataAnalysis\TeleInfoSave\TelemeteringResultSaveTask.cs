using Abp.Dependency;
using MongoDB.Bson;
using MongoDB.Driver;
using Polly;
using Polly.Retry;
using Quartz;
using Quartz.Impl;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using ToolLibrary;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Model;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.ISAS.MongoDB.Entities.DataMonitoring;
using Yunda.SOMS.DataMonitoringServer.DataAnalysis.TeleInfoSave;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.MongoDB.Repositories;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using System.Numerics;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using ApiEndpoints = Yunda.ISAS.DataMonitoringServer.DataAnalysis.Model.ApiEndpoints;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave
{
    public class TelemeteringResultSaveTask : ISingletonDependency, IDisposable
    {
        private readonly DataRepository _dataRepository;
        private readonly RunningDataCache _runningDataCache;
        private readonly RedisDataRepository _redisDataRepository;
        private readonly IMongoDbRepository<BsonDocument, Guid> _telemeteringModelRepository;
        private readonly IMongoDbRepository<TelemeteringStatisticsResult, Guid> _telemeteringStatisticsRepository;
        private readonly IRedisRepository<TelemeteringModel, string> _telemeteringModelListRedis;
        private readonly string _telemeteringModelListRediskey = "telemeteringModelList_oneminute";
        private readonly TelemeteringBucketSaveService _bucketSaveService;

        // 性能监控和重试策略
        private readonly ResiliencePipeline _retryPipeline;


    

        public TelemeteringResultSaveTask(DataRepository dataRepository,
            RunningDataCache runningDataCache,
            IRedisRepository<TelemeteringModel, string> telemeteringModelListRedis,
            IMongoDbRepository<BsonDocument, Guid> telemeteringModelRepository,
            IMongoDbRepository<TelemeteringStatisticsResult, Guid> telemeteringStatisticsRepository,
            RedisDataRepository redisDataRepository,
            TelemeteringBucketSaveService bucketSaveService)
        {
            _dataRepository = dataRepository;
            _runningDataCache = runningDataCache;
            _redisDataRepository = redisDataRepository;
            _telemeteringModelRepository = telemeteringModelRepository;
            _telemeteringStatisticsRepository = telemeteringStatisticsRepository;
            _telemeteringModelListRedis = telemeteringModelListRedis;
            _bucketSaveService = bucketSaveService;

            // 初始化重试策略 (Polly v8)
            _retryPipeline = new ResiliencePipelineBuilder()
                .AddRetry(new RetryStrategyOptions
                {
                    ShouldHandle = new PredicateBuilder().Handle<MongoException>().Handle<TimeoutException>(),
                    MaxRetryAttempts = 3,
                    Delay = TimeSpan.FromSeconds(1),
                    BackoffType = DelayBackoffType.Exponential,
                    OnRetry = args =>
                    {
                        Log4Helper.Warn(this.GetType(), $"重试第 {args.AttemptNumber} 次，延迟 {args.RetryDelay.TotalSeconds} 秒");
                        return new ValueTask(Task.CompletedTask);
                    }
                })
                .Build();
        }

        public async Task StartTelemeteringSaveJobs(
         IEnumerable<ConnectionConfig> connections,
         RedisDataRepository redisRepo,
         IMongoDbRepository<BsonDocument, Guid> modelRepo,
         IMongoDbRepository<TelemeteringStatisticsResult, Guid> statisticsRepo,
         RunningDataCache cache)
        {
            StdSchedulerFactory factory = new StdSchedulerFactory();
            IScheduler scheduler = await factory.GetScheduler();
            await scheduler.Start();
            foreach (var connection in connections)
            {
                if (connection.SaveMode != (int)SaveModeEnum.Interval && connection.SaveMode != (int)SaveModeEnum.Both)
                    continue;

                foreach (FixedIntervalEnum interval in Enum.GetValues(typeof(FixedIntervalEnum)))
                {
                    string jobName = $"Job_{connection.Ip}_{interval}";
                    string triggerName = $"Trigger_{connection.Ip}_{interval}";
                    string groupName = "TelemeteringJobs";

                    JobKey jobKey = new JobKey(jobName, groupName);
                    TriggerKey triggerKey = new TriggerKey(triggerName, groupName);

                    // ✅ 避免重复注册
                    if (await scheduler.CheckExists(jobKey))
                    {
                        Log4Helper.Info(this.GetType(), $"已存在 Job [{jobKey}], 跳过注册。");
                        continue;
                    }

                    IJobDetail job = JobBuilder.Create<TelemeteringSaveJob>()
                        .WithIdentity(jobKey)
                        .UsingJobData(new JobDataMap
                        {
                    { "connection", connection },
                    { "interval", interval },
                    { "redisRepo", redisRepo },
                    { "modelRepo", modelRepo },
                    { "statisticsRepo", statisticsRepo },
                    { "cache", cache },
                    { "bucketSaveService", _bucketSaveService },
                        })
                        .Build();

                    ITrigger trigger;
                    if (interval <= FixedIntervalEnum.Hour12)
                    {
                        trigger = TriggerBuilder.Create()
                            .WithIdentity(triggerKey)
                            .StartNow()
                            .WithSimpleSchedule(x => x
                                .WithInterval(TimeSpan.FromSeconds((int)interval))
                                .RepeatForever())
                            .Build();
                    }
                    else
                    {
                        var cron = GetCronExpression(interval);
                        trigger = TriggerBuilder.Create()
                            .WithIdentity(triggerKey)
                            .WithCronSchedule(cron)
                            .Build();
                    }

                    await scheduler.ScheduleJob(job, trigger);
                    Log4Helper.Info(this.GetType(), $"已调度 Job [{jobKey}] - Interval={interval}");
                }
            }
        }
        private string GetCronExpression(FixedIntervalEnum interval)
        {
            return interval switch
            {
                FixedIntervalEnum.Day1 => "0 0 1 * * ?",        // 每天凌晨1点
                FixedIntervalEnum.Day7 => "0 0 1 ? * MON",      // 每周一凌晨1点
                FixedIntervalEnum.Day30 => "0 0 1 1 * ?",       // 每月1号凌晨1点
                _ => throw new NotSupportedException($"不支持该 interval 的 Cron 表达式：{interval}")
            };
        }

        // 改用线程安全的队列和改进的缓存结构
        private readonly ConcurrentDictionary<string, ConcurrentQueue<TelemeteringModel>> _cache = new ConcurrentDictionary<string, ConcurrentQueue<TelemeteringModel>>();
        private readonly ConcurrentDictionary<string, int> _cacheSizes = new ConcurrentDictionary<string, int>();
        private Timer _cacheSaveTimer;
        private readonly object _timerLock = new object();
        private volatile bool _disposed = false;

        /// <summary>
        /// 验证遥测数据的有效性
        /// </summary>
        private bool ValidateTelemetryData(TelemeteringModel model)
        {
            if (model == null) return false;
            if (model.Id == Guid.Empty) return false;

            // 使用配置类进行验证
            return BucketingConfiguration.Validation.IsValidTelemetryData(model.ResultValue, model.ResultTime);
        }

        /// <summary>
        /// 改进的缓存保存方法 - 修复并发问题
        /// </summary>
        public async Task SaveWithCacheAsync(TelemeteringModel model, ConnectionConfig connectionConfig)
        {
            if (_disposed)
            {
                Log4Helper.Warn(this.GetType(), "[SaveWithCacheAsync] 服务已释放，跳过处理。");
                return;
            }

            // 参数验证
            if (!ValidateTelemetryData(model) || connectionConfig == null)
            {
                Log4Helper.Error(this.GetType(), "[SaveWithCacheAsync] 参数验证失败，跳过处理。");
                return;
            }

            bool isSave = connectionConfig.SaveMode == (int)SaveModeEnum.Change ||
                          connectionConfig.SaveMode == (int)SaveModeEnum.Both;
            if (!isSave)
            {
                Log4Helper.Debug(this.GetType(), "[SaveWithCacheAsync] SaveMode 不符合条件，跳过保存。");
                return;
            }

            string key;
            try
            {
                key = ((DataSourceCategoryEnum)connectionConfig.DataSourceCategoryName).ToString();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[SaveWithCacheAsync] DataSourceCategoryName 转换失败：{connectionConfig.DataSourceCategoryName}", ex);
                return;
            }

            try
            {
                using (TelemetryMetrics.MeasureTime("save_with_cache"))
                {
                    // 检查缓存大小限制
                    var currentSize = _cacheSizes.GetOrAdd(key, 0);
                    if (currentSize >= BucketingConfiguration.Performance.MAX_CACHE_SIZE_PER_CATEGORY)
                    {
                        Log4Helper.Warn(this.GetType(), $"[SaveWithCacheAsync] 缓存已满，key: {key}, 当前大小: {currentSize}");
                        TelemetryMetrics.IncrementCounter("cache_full_errors");
                        return;
                    }

                    // 使用线程安全的队列
                    var queue = _cache.GetOrAdd(key, _ => new ConcurrentQueue<TelemeteringModel>());
                    queue.Enqueue(model);
                    var newSize = _cacheSizes.AddOrUpdate(key, 1, (_, count) => count + 1);

                    // 更新缓存大小指标
                    TelemetryMetrics.SetGauge(TelemetryMetrics.Gauges.CACHE_SIZE, newSize);
                    TelemetryMetrics.IncrementCounter(TelemetryMetrics.Counters.TELEMETRY_RECEIVED);

                    // 异步保存到分桶存储
                    await _retryPipeline.ExecuteAsync(async _ =>
                    {
                        await _bucketSaveService.SaveToBucket(model, connectionConfig);
                    });

                    TelemetryMetrics.RecordSuccess("save_with_cache");
                }
            }
            catch (Exception ex)
            {
                TelemetryMetrics.RecordError("save_with_cache", ex);
                Log4Helper.Error(this.GetType(), $"[SaveWithCacheAsync] 缓存更新失败，key: {key}", ex);
                return;
            }

            // 初始化定时器（双重检查锁定模式）
            if (_cacheSaveTimer == null && !_disposed)
            {
                lock (_timerLock)
                {
                    if (_cacheSaveTimer == null && !_disposed)
                    {
                        var interval = TimeSpan.FromMinutes(BucketingConfiguration.Performance.CACHE_SAVE_INTERVAL_MINUTES);
                        _cacheSaveTimer = new Timer(async _ => await ProcessCacheAsync(),
                            null,
                            interval,
                            interval);

                        Log4Helper.Info(this.GetType(), "[SaveWithCacheAsync] 缓存定时器已启动");
                    }
                }
            }
        }

        /// <summary>
        /// 处理缓存数据的异步方法
        /// </summary>
        private async Task ProcessCacheAsync()
        {
            if (_disposed) return;

            try
            {
                var categories = _cache.Keys.ToList();
                foreach (var category in categories)
                {
                    if (_disposed) break;

                    var dataBatch = new List<TelemeteringModel>();

                    // 从队列中取出数据
                    if (_cache.TryGetValue(category, out var queue))
                    {
                        var batchSize = Math.Min(BucketingConfiguration.Performance.BATCH_SIZE, _cacheSizes.GetOrAdd(category, 0));
                        for (int i = 0; i < batchSize && queue.TryDequeue(out var item); i++)
                        {
                            dataBatch.Add(item);
                            _cacheSizes.AddOrUpdate(category, 0, (_, count) => Math.Max(0, count - 1));
                        }

                        // 更新缓存大小指标
                        TelemetryMetrics.SetGauge($"{TelemetryMetrics.Gauges.CACHE_SIZE}_{category}", _cacheSizes.GetOrAdd(category, 0));
                    }

                    if (dataBatch.Count > 0)
                    {
                        await _retryPipeline.ExecuteAsync(async _ =>
                        {
                            await SaveBatchToDatabase(dataBatch, category);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[ProcessCacheAsync] 处理缓存异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量保存数据到数据库
        /// </summary>
        private async Task SaveBatchToDatabase(List<TelemeteringModel> dataBatch, string category)
        {
            try
            {
                _telemeteringModelRepository.CollectionName = $"{nameof(TelemeteringModel)}_{category}{DateTime.Now:yyyyMMdd}";

                var documents = new List<BsonDocument>();
                foreach (var t in dataBatch)
                {
                    if (t.EquipmentInfoId == null ||
                        !_runningDataCache.EquipmentInfoSimDic.TryGetValue(t.EquipmentInfoId.Value, out var equipment))
                    {
                        Log4Helper.Warn(this.GetType(), $"[SaveBatchToDatabase] 无效 EquipmentInfoId: {t.EquipmentInfoId}");
                        continue;
                    }

                    var result = new TelemeteringResult
                    {
                        TelemeteringConfigurationId = t.Id,
                        Name = t.Name,
                        EquipmentInfoName = equipment.Name,
                        ResultTime = t.ResultTime,
                        ResultValue = t.ResultValue,
                        SaveMethod = 2,
                        Unit = t.Unit,
                    };

                    documents.Add(result.ToBsonDocument());
                }

                if (documents.Count > 0)
                {
                    await _telemeteringModelRepository.InsertManyAsync(documents);
                    Log4Helper.Debug(this.GetType(), $"[SaveBatchToDatabase] 成功保存 {documents.Count} 条数据，category: {category}");
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[SaveBatchToDatabase] 数据批量保存失败，category: {category}", ex);
                throw; // 重新抛出异常以触发重试
            }
        }

        /// <summary>
        /// 资源释放
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;

            try
            {
                // 停止定时器
                _cacheSaveTimer?.Dispose();

                // 处理剩余的缓存数据
                var finalProcessTask = ProcessCacheAsync();
                finalProcessTask.Wait(TimeSpan.FromSeconds(30)); // 最多等待30秒

                Log4Helper.Info(this.GetType(), "[Dispose] 资源已释放");
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[Dispose] 释放资源时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保持向后兼容的同步方法
        /// </summary>
        public async void SaveWithCache(TelemeteringModel model, ConnectionConfig connectionConfig)
        {
            try
            {
                await SaveWithCacheAsync(model, connectionConfig);
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[SaveWithCache] 异步调用失败: {ex.Message}", ex);
            }
        }

        internal void SaveStart(IEnumerable<ConnectionConfig> connections)
        {
            StartTelemeteringSaveJobs(connections, _redisDataRepository, _telemeteringModelRepository, _telemeteringStatisticsRepository, _runningDataCache);
        }
    }

    public class TelemeteringSaveJob : IJob
    {
        public async Task Execute(IJobExecutionContext context)
        {
            var dataMap = context.MergedJobDataMap;

            var connection = (ConnectionConfig)dataMap["connection"];
            var interval = (FixedIntervalEnum)dataMap["interval"];
            var redisRepo = (RedisDataRepository)dataMap["redisRepo"];
            var modelRepo = (IMongoDbRepository<BsonDocument, Guid>)dataMap["modelRepo"];
            var statisticsRepo = (IMongoDbRepository<TelemeteringStatisticsResult, Guid>)dataMap["statisticsRepo"];
            var cache = (RunningDataCache)dataMap["cache"];
            var bucketSaveService = (TelemeteringBucketSaveService)dataMap["bucketSaveService"];
            try
            {
                string redisKey = redisRepo.TelemeteringModelListRediskey + "_" + (DataSourceCategoryEnum)connection.DataSourceCategoryName;
                var ycDatas = await redisRepo.TelemeteringModelListRedis.HashSetGetAllAsync(redisKey);

                // 设置遥测结果表集合名称
                string category = ((DataSourceCategoryEnum)connection.DataSourceCategoryName).ToString();
                modelRepo.CollectionName = $"{nameof(TelemeteringModel)}_{category}{DateTime.Now:yyyyMMdd}";

                var documents = new List<BsonDocument>();

                // 先保存当前数据
                foreach (var t in ycDatas)
                {
                    if (t.EquipmentInfoId == null || !cache.EquipmentInfoSimDic.TryGetValue(t.EquipmentInfoId.Value, out var equipment))
                    {
                        Log4Helper.Warn(this.GetType(), $"[{connection.Name}] 无效 EquipmentInfoId: {t.EquipmentInfoId}");
                        continue;
                    }
                    var result = new TelemeteringResult
                    {
                        TelemeteringConfigurationId = t.Id,
                        Name = t.Name,
                        EquipmentInfoName = equipment.Name,
                        ResultTime = t.ResultTime,
                        ResultValue = t.ResultValue,
                        SaveMethod = 2,
                        Unit = t.Unit,
                    };

                    documents.Add(result.ToBsonDocument());
                }

                // 插入当前数据
                if (documents.Count > 0)
                {
                    await modelRepo.InsertManyAsync(documents);
                }

                // 按遥测点分别处理统计数据
                foreach (var t in ycDatas)
                {
                    if (t.EquipmentInfoId == null || !cache.EquipmentInfoSimDic.TryGetValue(t.EquipmentInfoId.Value, out var equipment))
                    {
                        continue;
                    }
                    // 处理统计数据 - 从MongoDB查询历史数据
                    await ProcessStatisticsData(t, interval, statisticsRepo, modelRepo, bucketSaveService, equipment);
                }


            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[{connection.Name}] Quartz Job 异常（{interval}）: {ex}");
            }
        }


        private string GetIntervalSuffix(FixedIntervalEnum interval) => interval switch
        {
            FixedIntervalEnum.Minute1 => "1min",
            FixedIntervalEnum.Minute5 => "5min",
            FixedIntervalEnum.Minute30 => "30min",
            FixedIntervalEnum.Hour1 => "1h",
            FixedIntervalEnum.Hour6 => "6h",
            FixedIntervalEnum.Hour12 => "12h",
            FixedIntervalEnum.Day1 => "1d",
            FixedIntervalEnum.Day7 => "7d",
            FixedIntervalEnum.Day30 => "30d",
            _ => "unknown"
        };

        /// <summary>
        /// 根据统计间隔获取开始时间
        /// </summary>
        /// <param name="endTime">结束时间</param>
        /// <param name="interval">统计间隔</param>
        /// <returns>开始时间</returns>
        private DateTime GetStartTimeByInterval(DateTime endTime, FixedIntervalEnum interval)
        {
            return interval switch
            {
                FixedIntervalEnum.Minute1 => endTime.AddMinutes(-1),
                FixedIntervalEnum.Minute5 => endTime.AddMinutes(-5),
                FixedIntervalEnum.Minute30 => endTime.AddMinutes(-30),
                FixedIntervalEnum.Hour1 => endTime.AddHours(-1),
                FixedIntervalEnum.Hour6 => endTime.AddHours(-6),
                FixedIntervalEnum.Hour12 => endTime.AddHours(-12),
                FixedIntervalEnum.Day1 => endTime.AddDays(-1),
                FixedIntervalEnum.Day7 => endTime.AddDays(-7),
                FixedIntervalEnum.Day30 => endTime.AddDays(-30),
                _ => endTime.AddHours(-1) // 默认取1小时
            };
        }

        /// <summary>
        /// 处理统计数据
        /// </summary>
        /// <param name="telemetry">遥测数据</param>
        /// <param name="interval">时间间隔</param>
        /// <param name="statisticsRepo">统计数据仓储</param>
        /// <param name="modelRepo">历史数据仓储</param>
        /// <param name="equipment">设备信息</param>
        /// <returns>异步任务</returns>
        private async Task ProcessStatisticsData(
            TelemeteringModel telemetry,
            FixedIntervalEnum interval,
            IMongoDbRepository<TelemeteringStatisticsResult, Guid> statisticsRepo,
            IMongoDbRepository<BsonDocument, Guid> modelRepo,
           TelemeteringBucketSaveService bucketSaveService ,
            dynamic equipment)
        {
            try
            {
                if (telemetry == null || telemetry.Id == Guid.Empty)
                {
                    Log4Helper.Warn(this.GetType(), $"处理统计数据时遥测数据为空或ID为空");
                    return;
                }

                // 检查telemetry.DataSourceCategory是否为null
                if (telemetry.DataSourceCategory == null)
                {
                    Log4Helper.Warn(this.GetType(), $"遥测点 {telemetry.Id} 的DataSourceCategory为空");
                    return;
                }

                // 使用遥测数据的时间而不是当前时间，确保时间一致性
                DateTime endTime = telemetry.ResultTime;
                DateTime startTime = GetStartTimeByInterval(endTime, interval);

                // 根据不同的统计间隔选择不同的集合
                List<TelemeteringModel> dataList = new List<TelemeteringModel>();

                // 分钟级数据从实时表查询
                if (interval <= FixedIntervalEnum.Minute30)
                {
                    try
                    {
                        // 从实时表查询
                        string category = ((DataSourceCategoryEnum)telemetry.DataSourceCategory).ToString();
                        modelRepo.CollectionName = $"{nameof(TelemeteringModel)}_{category}{DateTime.Now:yyyyMMdd}";

                        // 构建查询条件 - 使用 Builders<BsonDocument>.Filter 方式
                        var filterBuilder = Builders<BsonDocument>.Filter;
                        var filter = filterBuilder.And(
                            filterBuilder.Eq("TelemeteringConfigurationId", telemetry.Id),
                            filterBuilder.Gte("ResultTime", startTime),
                            filterBuilder.Lte("ResultTime", endTime)
                        );

                        // 执行查询
                        var historicalData = modelRepo.GetAllIncludeToFindFluent(filter).ToList();

                        if (historicalData != null && historicalData.Any())
                        {
                            foreach (var doc in historicalData)
                            {
                                if (doc != null &&
                                    doc.Contains("TelemeteringConfigurationId") &&
                                    doc.Contains("ResultTime") &&
                                    doc.Contains("ResultValue") &&
                                    doc.Contains("Name") &&
                                    doc.Contains("Unit"))
                                {
                                    try
                                    {
                                        DateTime recordTime;
                                        if (DateTime.TryParse(doc["ResultTime"].ToString(), out recordTime))
                                        {
                                            var model = new TelemeteringModel
                                            {
                                                Id = doc["TelemeteringConfigurationId"].AsGuid,
                                                Name = doc["Name"].AsString,
                                                ResultTime = recordTime,
                                                ResultValue = doc["ResultValue"].IsDouble ?
                                                    (float)doc["ResultValue"].AsDouble :
                                                    float.Parse(doc["ResultValue"].ToString()),
                                                Unit = doc["Unit"].AsString,
                                                DataSourceCategory = telemetry.DataSourceCategory // 继承原遥测点的数据源类型
                                            };
                                            dataList.Add(model);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Log4Helper.Error(this.GetType(), $"转换历史数据异常: {ex.Message}", ex);
                                        continue;
                                    }
                                }
                            }
                        }
                        else
                        {
                            Log4Helper.Info(this.GetType(), $"未找到遥测点 {telemetry.Id} 在时间范围 {startTime} - {endTime} 的历史数据");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), $"查询实时表历史数据异常: {ex.Message}", ex);
                    }
                }
                // 小时及以上级别数据从统计表查询
                else
                {
                    try
                    {
                        // 从统计表查询 - 按年份分表
                        statisticsRepo.CollectionName = $"TelemeteringStatisticsResult_{interval}_{startTime.Year}";

                        // 查询条件
                        var query = statisticsRepo.GetAll()
                            .Where(s => s.TelemeteringConfigurationId == telemetry.Id)
                            .Where(s => s.StatisticsDateTime >= startTime && s.StatisticsDateTime <= endTime)
                            .Where(s => s.StatisticsType == StatisticsTypeEnum.RealTime)
                            .ToList();

                        // 执行查询
                        if (query != null && query.Any())
                        {
                            foreach (var stat in query)
                            {
                                try
                                {
                                    var model = new TelemeteringModel
                                    {
                                        Id = stat.TelemeteringConfigurationId,
                                        Name = stat.Name,
                                        ResultTime = stat.ResultTime,
                                        ResultValue = stat.ResultValue,
                                        Unit = stat.Unit,
                                        DataSourceCategory = telemetry.DataSourceCategory // 继承原遥测点的数据源类型
                                    };
                                    dataList.Add(model);
                                }
                                catch (Exception ex)
                                {
                                    Log4Helper.Error(this.GetType(), $"转换统计数据异常: {ex.Message}", ex);
                                    continue;
                                }
                            }
                        }
                        else
                        {
                            Log4Helper.Info(this.GetType(), $"未找到遥测点 {telemetry.Id} 在时间范围 {startTime} - {endTime} 的统计数据");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), $"查询统计表历史数据异常: {ex.Message}", ex);
                    }
                }

                if (dataList == null || dataList.Count == 0)
                {
                    Log4Helper.Info(this.GetType(), $"遥测点 {telemetry.Id} 在时间范围 {startTime} - {endTime} 没有足够的历史数据进行统计");
                    return;
                }

                // 检查equipment参数是否包含必要属性
                string equipmentName;
                try
                {
                    equipmentName = equipment.Name;
                }
                catch (Exception ex)
                {
                    Log4Helper.Error(this.GetType(), $"获取设备信息异常: {ex.Message}", ex);
                    equipmentName = "未知设备";
                }

                // 计算统计值 - 使用遥测数据的时间确保一致性
                var statistics = new List<TelemeteringStatisticsResult>();
                DateTime statisticsTime = telemetry.ResultTime;

                try
                {
                    // 设置集合名称 - 按年份分表
                    statisticsRepo.CollectionName = $"TelemeteringStatisticsResult_{interval}_{statisticsTime.Year}";

                    // 1. 实时值 (包含了原TelemeteringResult的所有信息)
                    statistics.Add(new TelemeteringStatisticsResult
                    {
                        TelemeteringConfigurationId = telemetry.Id,
                        Name = telemetry.Name,
                        EquipmentInfoName = equipmentName,
                        ResultValue = telemetry.ResultValue,
                        Unit = telemetry.Unit,
                        ResultTime = telemetry.ResultTime, // 保留原始时间戳
                        StatisticsType = StatisticsTypeEnum.RealTime,
                        IntervalType = interval,
                        StatisticsDateTime = statisticsTime, // 使用一致的时间
                        DataCount = 1,
                    });

                    // 如果有多个数据点，计算其他统计值
                    if (dataList.Count > 1)
                    {
                        try
                        {
                            // 2. 最大值
                            float maxValue = dataList.Max(x => x.ResultValue);
                            statistics.Add(new TelemeteringStatisticsResult
                            {
                                TelemeteringConfigurationId = telemetry.Id,
                                Name = telemetry.Name,
                                EquipmentInfoName = equipmentName,
                                ResultValue = maxValue,
                                Unit = telemetry.Unit,
                                ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                StatisticsType = StatisticsTypeEnum.Maximum,
                                IntervalType = interval,
                                StatisticsDateTime = statisticsTime,
                                DataCount = dataList.Count,
                            });
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算最大值异常: {ex.Message}", ex);
                        }

                        try
                        {
                            // 3. 最小值
                            float minValue = dataList.Min(x => x.ResultValue);
                            statistics.Add(new TelemeteringStatisticsResult
                            {
                                TelemeteringConfigurationId = telemetry.Id,
                                Name = telemetry.Name,
                                EquipmentInfoName = equipmentName,
                                ResultValue = minValue,
                                Unit = telemetry.Unit,
                                ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                StatisticsType = StatisticsTypeEnum.Minimum,
                                IntervalType = interval,
                                StatisticsDateTime = statisticsTime,
                                DataCount = dataList.Count,
                            });
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算最小值异常: {ex.Message}", ex);
                        }

                        try
                        {
                            // 4. 平均值
                            float avgValue = dataList.Average(x => x.ResultValue);
                            statistics.Add(new TelemeteringStatisticsResult
                            {
                                TelemeteringConfigurationId = telemetry.Id,
                                Name = telemetry.Name,
                                EquipmentInfoName = equipmentName,
                                ResultValue = avgValue,
                                Unit = telemetry.Unit,
                                ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                StatisticsType = StatisticsTypeEnum.Average,
                                IntervalType = interval,
                                StatisticsDateTime = statisticsTime,
                                DataCount = dataList.Count,
                            });
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算平均值异常: {ex.Message}", ex);
                        }

                        try
                        {
                            // 5. 差值（最后一个值与第一个值的差）
                            var orderedData = dataList.OrderBy(x => x.ResultTime).ToList();
                            if (orderedData.Count >= 2)
                            {
                                var firstValue = orderedData.First().ResultValue;
                                var lastValue = orderedData.Last().ResultValue;
                                float diffValue = lastValue - firstValue;
                                statistics.Add(new TelemeteringStatisticsResult
                                {
                                    TelemeteringConfigurationId = telemetry.Id,
                                    Name = telemetry.Name,
                                    EquipmentInfoName = equipmentName,
                                    ResultValue = diffValue,
                                    Unit = telemetry.Unit,
                                    ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                    StatisticsType = StatisticsTypeEnum.Difference,
                                    IntervalType = interval,
                                    StatisticsDateTime = statisticsTime,
                                    DataCount = dataList.Count,
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算差值异常: {ex.Message}", ex);
                        }

                        try
                        {
                            // 6. 累计值（所有值的和）
                            float accumulatedValue = dataList.Sum(x => x.ResultValue);
                            statistics.Add(new TelemeteringStatisticsResult
                            {
                                TelemeteringConfigurationId = telemetry.Id,
                                Name = telemetry.Name,
                                EquipmentInfoName = equipmentName,
                                ResultValue = accumulatedValue,
                                Unit = telemetry.Unit,
                                ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                StatisticsType = StatisticsTypeEnum.Accumulated,
                                IntervalType = interval,
                                StatisticsDateTime = statisticsTime,
                                DataCount = dataList.Count,
                            });
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算累计值异常: {ex.Message}", ex);
                        }

                        try
                        {
                            // 7. 标准差
                            float mean = dataList.Average(x => x.ResultValue);
                            float sumOfSquaresOfDifferences = dataList.Sum(x => (x.ResultValue - mean) * (x.ResultValue - mean));
                            float standardDeviation = (float)Math.Sqrt(sumOfSquaresOfDifferences / dataList.Count);
                            statistics.Add(new TelemeteringStatisticsResult
                            {
                                TelemeteringConfigurationId = telemetry.Id,
                                Name = telemetry.Name,
                                EquipmentInfoName = equipmentName,
                                ResultValue = standardDeviation,
                                Unit = telemetry.Unit,
                                ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                StatisticsType = StatisticsTypeEnum.StandardDeviation,
                                IntervalType = interval,
                                StatisticsDateTime = statisticsTime,
                                DataCount = dataList.Count,
                            });
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算标准差异常: {ex.Message}", ex);
                        }

                        try
                        {
                            // 8. 中位数
                            var sortedValues = dataList.Select(x => x.ResultValue).OrderBy(x => x).ToList();
                            float median;
                            int count = sortedValues.Count;
                            if (count % 2 == 0 && count > 0)
                            {
                                // 偶数个数据点，取中间两个的平均值
                                median = (sortedValues[count / 2 - 1] + sortedValues[count / 2]) / 2;
                            }
                            else if (count > 0)
                            {
                                // 奇数个数据点，取中间的值
                                median = sortedValues[count / 2];
                            }
                            else
                            {
                                median = 0; // 防御性编程
                            }

                            statistics.Add(new TelemeteringStatisticsResult
                            {
                                TelemeteringConfigurationId = telemetry.Id,
                                Name = telemetry.Name,
                                EquipmentInfoName = equipmentName,
                                ResultValue = median,
                                Unit = telemetry.Unit,
                                ResultTime = telemetry.ResultTime, // 保留原始时间戳
                                StatisticsType = StatisticsTypeEnum.Median,
                                IntervalType = interval,
                                StatisticsDateTime = statisticsTime,
                                DataCount = dataList.Count,
                            });
                        }
                        catch (Exception ex)
                        {
                            Log4Helper.Error(this.GetType(), $"计算中位数异常: {ex.Message}", ex);
                        }
                    }

                    // 批量插入统计结果
                    if (statistics.Count > 0)
                    {
                        await statisticsRepo.InsertManyAsync(statistics);
                        Log4Helper.Debug(this.GetType(), $"成功保存遥测点 {telemetry.Id} 的 {statistics.Count} 条统计数据，间隔类型：{interval}");
                        
                        // 将统计结果保存到分桶存储
                        await bucketSaveService.SaveStatisticsToBucket(statistics, telemetry, interval, equipmentName);
                    }
                }
                catch (Exception ex)
                {
                    Log4Helper.Error(this.GetType(), $"保存统计数据异常: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"处理统计数据异常: {ex.Message}", ex);
            }
        }
    }
}