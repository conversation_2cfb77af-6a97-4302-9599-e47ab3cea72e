# MongoDB Configuration File
# This is a sample configuration file for MongoDB
# Copy this to your MongoDB installation directory and modify as needed

# Storage settings
storage:
  dbPath: "D:\\SOMS\\Data\\MongoDB\\data"
  journal:
    enabled: true
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 2
      journalCompressor: snappy
      directoryForIndexes: false
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# Network settings
net:
  port: 37017
  bindIp: 127.0.0.1
  maxIncomingConnections: 200
  wireObjectCheck: true
  ipv6: false

# Process management
processManagement:
  fork: false
  pidFilePath: "D:\\SOMS\\Data\\MongoDB\\data\\mongod.pid"

# Logging
systemLog:
  destination: file
  path: "D:\\SOMS\\Logs\\mongod.log"
  logAppend: true
  logRotate: rename
  verbosity: 0
  component:
    accessControl:
      verbosity: 0
    command:
      verbosity: 0
    control:
      verbosity: 0
    geo:
      verbosity: 0
    index:
      verbosity: 0
    network:
      verbosity: 0
    query:
      verbosity: 0
    replication:
      verbosity: 0
    sharding:
      verbosity: 0
    storage:
      verbosity: 0
      journal:
        verbosity: 0
    write:
      verbosity: 0

# Security settings (uncomment if authentication is needed)
# security:
#   authorization: enabled
#   keyFile: "D:\\SOMS\\Data\\MongoDB\\keyfile"

# Operation profiling
operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp

# Replication (uncomment if using replica sets)
# replication:
#   replSetName: "rs0"

# Sharding (uncomment if using sharding)
# sharding:
#   clusterRole: configsvr

# Performance tuning
setParameter:
  enableLocalhostAuthBypass: false
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
  maxLogSizeKB: 10240
  logLevel: 0
  syncdelay: 60
  notablescan: false
  directoryperdb: false
  
# WiredTiger specific settings
wiredTigerCacheSizeGB: 2
wiredTigerCollectionBlockCompressor: snappy
wiredTigerIndexPrefixCompression: true
