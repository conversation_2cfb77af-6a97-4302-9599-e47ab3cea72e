using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;
using RunMongoDB.Services;

namespace RunMongoDB
{
    /// <summary>
    /// Test program to validate all enhanced MongoDB management functionality
    /// </summary>
    public class TestProgram
    {
        public static async Task Main(string[] args)
        {
            // Configure logging for testing
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File(@"D:\SOMS\Logs\RunMongoDB-Test-.txt", rollingInterval: RollingInterval.Day)
                .MinimumLevel.Debug()
                .CreateLogger();

            try
            {
                Log.Information("=== MongoDB Management Service Test Suite ===");

                // Load configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();

                var settings = configuration.GetSection("MongoDBSetting").Get<MongoDBSettings>();
                
                if (settings == null)
                {
                    Log.Fatal("Failed to load MongoDB settings");
                    return;
                }

                Log.Information("Configuration loaded successfully");
                await RunTestSuiteAsync(settings);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Test suite failed with fatal error");
            }
            finally
            {
                Log.Information("=== Test Suite Completed ===");
                Log.CloseAndFlush();
            }
        }

        private static async Task RunTestSuiteAsync(MongoDBSettings settings)
        {
            Log.Information("Starting comprehensive test suite...");

            // Test 1: Process Manager
            await TestProcessManagerAsync(settings);

            // Test 2: Health Monitor
            await TestHealthMonitorAsync(settings);

            // Test 3: Database Initializer
            await TestDatabaseInitializerAsync(settings);

            // Test 4: Performance Optimizer
            await TestPerformanceOptimizerAsync(settings);

            // Test 5: Backup Service
            await TestBackupServiceAsync(settings);

            // Test 6: Enhanced Worker (short test)
            await TestEnhancedWorkerAsync(settings);

            Log.Information("All tests completed");
        }

        private static async Task TestProcessManagerAsync(MongoDBSettings settings)
        {
            Log.Information("--- Testing MongoDB Process Manager ---");
            
            try
            {
                var processManager = new MongoDBProcessManager(settings);

                // Test process detection
                var isRunning = processManager.IsMongoDBRunning();
                Log.Information("MongoDB process running: {IsRunning}", isRunning);

                if (!isRunning)
                {
                    // Test startup
                    Log.Information("Testing MongoDB startup...");
                    var started = await processManager.StartMongoDBAsync();
                    Log.Information("MongoDB startup result: {Started}", started);

                    if (started)
                    {
                        await Task.Delay(5000); // Wait for startup
                        
                        // Test restart
                        Log.Information("Testing MongoDB restart...");
                        var restarted = await processManager.RestartMongoDBAsync();
                        Log.Information("MongoDB restart result: {Restarted}", restarted);
                    }
                }

                Log.Information("Process Manager tests completed");
            }
            catch (Exception ex)
            {
                Log.Error("Process Manager test failed: {Error}", ex.Message);
            }
        }

        private static async Task TestHealthMonitorAsync(MongoDBSettings settings)
        {
            Log.Information("--- Testing MongoDB Health Monitor ---");
            
            try
            {
                var healthMonitor = new MongoDBHealthMonitor(settings);

                // Perform health check
                var healthResult = await healthMonitor.PerformHealthCheckAsync();
                
                Log.Information("Health check completed:");
                Log.Information("  Overall Health: {IsHealthy}", healthResult.IsHealthy);
                Log.Information("  Process Running: {ProcessRunning}", healthResult.ProcessRunning);
                Log.Information("  Database Connectable: {DatabaseConnectable}", healthResult.DatabaseConnectable);
                Log.Information("  Memory Usage: {MemoryUsageMB} MB", healthResult.MemoryUsageMB);
                Log.Information("  Active Connections: {ActiveConnections}", healthResult.ActiveConnections);
                Log.Information("  CPU Usage: {CpuUsagePercent:F1}%", healthResult.CpuUsagePercent);
                Log.Information("  Disk Free Space: {FreeSpacePercent:F1}%", healthResult.DiskSpaceInfo.FreeSpacePercent);

                if (healthResult.Issues.Count > 0)
                {
                    Log.Warning("Health issues detected: {Issues}", string.Join(", ", healthResult.Issues));
                }

                Log.Information("Health Monitor tests completed");
            }
            catch (Exception ex)
            {
                Log.Error("Health Monitor test failed: {Error}", ex.Message);
            }
        }

        private static async Task TestDatabaseInitializerAsync(MongoDBSettings settings)
        {
            Log.Information("--- Testing MongoDB Database Initializer ---");
            
            try
            {
                var initializer = new MongoDBInitializer(settings);

                // Test database initialization
                var initialized = await initializer.InitializeDatabaseAsync();
                Log.Information("Database initialization result: {Initialized}", initialized);

                Log.Information("Database Initializer tests completed");
            }
            catch (Exception ex)
            {
                Log.Error("Database Initializer test failed: {Error}", ex.Message);
            }
        }

        private static async Task TestPerformanceOptimizerAsync(MongoDBSettings settings)
        {
            Log.Information("--- Testing MongoDB Performance Optimizer ---");
            
            try
            {
                var optimizer = new MongoDBPerformanceOptimizer(settings);

                // Get performance metrics
                var metrics = await optimizer.GetPerformanceMetricsAsync();
                Log.Information("Performance metrics:");
                Log.Information("  Insert Operations: {InsertOps}", metrics.InsertOps);
                Log.Information("  Query Operations: {QueryOps}", metrics.QueryOps);
                Log.Information("  Update Operations: {UpdateOps}", metrics.UpdateOps);
                Log.Information("  Delete Operations: {DeleteOps}", metrics.DeleteOps);
                Log.Information("  Current Connections: {CurrentConnections}", metrics.CurrentConnections);
                Log.Information("  Available Connections: {AvailableConnections}", metrics.AvailableConnections);
                Log.Information("  Resident Memory: {ResidentMemoryMB} MB", metrics.ResidentMemoryMB);

                // Test optimization (if enabled and enough time has passed)
                if (settings.EnablePerformanceOptimization)
                {
                    Log.Information("Testing performance optimization...");
                    var optimized = await optimizer.OptimizePerformanceAsync();
                    Log.Information("Performance optimization result: {Optimized}", optimized);
                }

                Log.Information("Performance Optimizer tests completed");
            }
            catch (Exception ex)
            {
                Log.Error("Performance Optimizer test failed: {Error}", ex.Message);
            }
        }

        private static async Task TestBackupServiceAsync(MongoDBSettings settings)
        {
            Log.Information("--- Testing MongoDB Backup Service ---");
            
            try
            {
                var backupService = new MongoDBBackupService(settings);

                if (settings.EnableAutoBackup)
                {
                    // Test backup creation
                    Log.Information("Testing backup creation...");
                    var backupResult = await backupService.CreateBackupAsync("test_backup");
                    
                    Log.Information("Backup creation result:");
                    Log.Information("  Success: {Success}", backupResult.Success);
                    Log.Information("  Backup Name: {BackupName}", backupResult.BackupName);
                    Log.Information("  Duration: {Duration}", backupResult.Duration);
                    
                    if (backupResult.Success)
                    {
                        Log.Information("  Backup Path: {BackupPath}", backupResult.BackupPath);
                        Log.Information("  Backup Size: {BackupSizeMB} MB", backupResult.BackupSizeBytes / (1024 * 1024));
                    }
                    else
                    {
                        Log.Warning("  Error: {ErrorMessage}", backupResult.ErrorMessage);
                    }
                }
                else
                {
                    Log.Information("Auto backup is disabled, skipping backup tests");
                }

                Log.Information("Backup Service tests completed");
            }
            catch (Exception ex)
            {
                Log.Error("Backup Service test failed: {Error}", ex.Message);
            }
        }

        private static async Task TestEnhancedWorkerAsync(MongoDBSettings settings)
        {
            Log.Information("--- Testing Enhanced Worker (Short Test) ---");
            
            try
            {
                var worker = new EnhancedWorker(settings);

                Log.Information("Starting Enhanced Worker for 30 seconds...");
                
                // Start worker in background
                var workerTask = Task.Run(async () => await worker.ExecuteAsync());
                
                // Let it run for 30 seconds
                await Task.Delay(30000);
                
                // Stop worker
                worker.Stop();
                
                // Wait for worker to stop (with timeout)
                var completedTask = await Task.WhenAny(workerTask, Task.Delay(10000));
                
                if (completedTask == workerTask)
                {
                    Log.Information("Enhanced Worker stopped gracefully");
                }
                else
                {
                    Log.Warning("Enhanced Worker did not stop within timeout");
                }

                Log.Information("Enhanced Worker tests completed");
            }
            catch (Exception ex)
            {
                Log.Error("Enhanced Worker test failed: {Error}", ex.Message);
            }
        }
    }
}
